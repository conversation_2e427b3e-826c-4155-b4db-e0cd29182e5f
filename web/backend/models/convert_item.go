package models

import "time"

// ConvertItem represents a file in the system
type ConvertItem struct {
	ID                   int64    `json:"id"`
	Filename             string   `json:"filename"`
	Location             string   `json:"location"`
	Duration             float64  `json:"duration"`
	Status               int      `json:"status"`
	Size                 int64    `json:"size"`
	StorageType          int      `json:"storage_type"`
	CreatedAt            string   `json:"created_at"`
	UpdatedAt            string   `json:"updated_at"`
	CLocation            string   `json:"c_location"`
	Name                 string   `json:"name"`
	Description          string   `json:"description"`
	Episode              string   `json:"episode"`
	Width                *int     `json:"width"`
	Height               *int     `json:"height"`
	FPS                  *float64 `json:"fps"`
	VideoCodec           *string  `json:"video_codec"`
	AudioCodec           *string  `json:"audio_codec"`
	Bitrate              *int     `json:"bitrate"`
	RecorderID           *int     `json:"recorder_id"`
	CodecSettingsVersion *int     `json:"codec_settings_version"`
}

// ConvertItemUpdateInput represents the input for updating a convert item
type ConvertItemUpdateInput struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Episode     string `json:"episode"`
}

// ConvertItemListResult represents the result of listing convert items
type ConvertItemListResult struct {
	Items      []ConvertItem `json:"items"`
	TotalItems int           `json:"total_items"`
	TotalPages int           `json:"total_pages"`
	Page       int           `json:"page"`
	Limit      int           `json:"limit"`
}

// FileStatus represents the status of a file
type FileStatus int

const (
	// FileStatusSuccess represents a successful file upload
	FileStatusSuccess FileStatus = 0
	// FileStatusQueue represents a file in the queue
	FileStatusQueue FileStatus = 1
	// FileStatusFailed represents a failed file upload
	FileStatusFailed FileStatus = 2
	// FileStatusProcessing represents a file being processed
	FileStatusProcessing FileStatus = 3
)

// StorageType represents the type of storage
type StorageType int

const (
	// StorageTypeLocal represents local storage
	StorageTypeLocal StorageType = 0
	// StorageTypeS3 represents S3 storage
	StorageTypeS3 StorageType = 1
	// StorageTypeBackblaze represents Backblaze B2 storage
	StorageTypeBackblaze StorageType = 2
)

// FileStatusUpdate represents a status update for a file
type FileStatusUpdate struct {
	ID     int64 `json:"id"`
	Status int   `json:"status"`
}

// NewConvertItem creates a new ConvertItem
func NewConvertItem(filename, location string, duration float64, size int64) ConvertItem {
	now := time.Now().UTC().Format(time.RFC3339)
	return ConvertItem{
		Filename:    filename,
		Name:        filename,
		Location:    location,
		Duration:    duration,
		Status:      int(FileStatusSuccess),
		Size:        size,
		StorageType: int(StorageTypeLocal),
		CreatedAt:   now,
		UpdatedAt:   now,
		CLocation:   location, // Set CLocation to the same value as Location
	}
}
