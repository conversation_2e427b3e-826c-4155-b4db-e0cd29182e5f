package models

// CloudStorageSettings represents cloud storage configuration
type CloudStorageSettings struct {
	ID                int    `json:"id"`
	StorageType       int    `json:"storage_type"`       // 0=Local, 1=S3, 2=Backblaze
	BackblazeKeyID    string `json:"backblaze_key_id"`
	BackblazeAppKey   string `json:"backblaze_app_key"`
	BackblazeBucket   string `json:"backblaze_bucket"`
	BackblazeEndpoint string `json:"backblaze_endpoint"`
	IsEnabled         bool   `json:"is_enabled"`
	CreatedAt         string `json:"created_at"`
	UpdatedAt         string `json:"updated_at"`
}

// CloudStorageSettingsInput represents input for updating cloud storage settings
type CloudStorageSettingsInput struct {
	StorageType       int    `json:"storage_type" binding:"required"`
	BackblazeKeyID    string `json:"backblaze_key_id"`
	BackblazeAppKey   string `json:"backblaze_app_key"`
	BackblazeBucket   string `json:"backblaze_bucket"`
	BackblazeEndpoint string `json:"backblaze_endpoint"`
	IsEnabled         bool   `json:"is_enabled"`
}

// BackblazeBucket represents a Backblaze B2 bucket
type BackblazeBucket struct {
	BucketID   string `json:"bucket_id"`
	BucketName string `json:"bucket_name"`
	BucketType string `json:"bucket_type"`
}

// DefaultCloudStorageSettings returns default cloud storage settings
func DefaultCloudStorageSettings() CloudStorageSettings {
	return CloudStorageSettings{
		StorageType:       int(StorageTypeLocal),
		BackblazeKeyID:    "",
		BackblazeAppKey:   "",
		BackblazeBucket:   "",
		BackblazeEndpoint: "https://s3.us-west-002.backblazeb2.com",
		IsEnabled:         false,
	}
}
