package repository

import (
	"database/sql"
	"showfer-web/models"
	"showfer-web/service/logger"
	"time"
)

// CloudStorageRepository handles cloud storage settings database operations
type CloudStorageRepository struct {
	db *sql.DB
}

// NewCloudStorageRepository creates a new CloudStorageRepository
func NewCloudStorageRepository(db *sql.DB) *CloudStorageRepository {
	return &CloudStorageRepository{db: db}
}

// CreateCloudStorageSettingsTable creates the cloud_storage_settings table
func CreateCloudStorageSettingsTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS cloud_storage_settings (
			id SERIAL PRIMARY KEY,
			storage_type INTEGER DEFAULT 0,
			backblaze_key_id TEXT,
			backblaze_app_key TEXT,
			backblaze_bucket TEXT,
			backblaze_endpoint TEXT DEFAULT 'https://s3.us-west-002.backblazeb2.com',
			is_enabled BOOLEAN DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		)
	`)
	if err != nil {
		logger.Error("Failed to create cloud_storage_settings table: %v", err)
		return err
	}

	// Insert default settings if table is empty
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM cloud_storage_settings").Scan(&count)
	if err != nil {
		logger.Error("Failed to count cloud storage settings: %v", err)
		return err
	}

	if count == 0 {
		defaultSettings := models.DefaultCloudStorageSettings()
		now := time.Now().UTC().Format(time.RFC3339)
		_, err = db.Exec(`
			INSERT INTO cloud_storage_settings (
				storage_type, backblaze_key_id, backblaze_app_key, 
				backblaze_bucket, backblaze_endpoint, is_enabled, 
				created_at, updated_at
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		`, defaultSettings.StorageType, defaultSettings.BackblazeKeyID,
			defaultSettings.BackblazeAppKey, defaultSettings.BackblazeBucket,
			defaultSettings.BackblazeEndpoint, defaultSettings.IsEnabled,
			now, now)
		if err != nil {
			logger.Error("Failed to insert default cloud storage settings: %v", err)
			return err
		}
		logger.Log("Inserted default cloud storage settings")
	}

	return nil
}

// GetCloudStorageSettings retrieves the cloud storage settings
func (repo *CloudStorageRepository) GetCloudStorageSettings() (models.CloudStorageSettings, error) {
	var settings models.CloudStorageSettings
	err := repo.db.QueryRow(`
		SELECT id, storage_type, backblaze_key_id, backblaze_app_key,
			   backblaze_bucket, backblaze_endpoint, is_enabled,
			   created_at, updated_at
		FROM cloud_storage_settings
		ORDER BY id ASC
		LIMIT 1
	`).Scan(
		&settings.ID, &settings.StorageType, &settings.BackblazeKeyID,
		&settings.BackblazeAppKey, &settings.BackblazeBucket,
		&settings.BackblazeEndpoint, &settings.IsEnabled,
		&settings.CreatedAt, &settings.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			// Return default settings if none exist
			return models.DefaultCloudStorageSettings(), nil
		}
		logger.Error("Failed to get cloud storage settings: %v", err)
		return models.DefaultCloudStorageSettings(), err
	}

	return settings, nil
}

// UpdateCloudStorageSettings updates the cloud storage settings
func (repo *CloudStorageRepository) UpdateCloudStorageSettings(input models.CloudStorageSettingsInput) error {
	now := time.Now().UTC().Format(time.RFC3339)
	
	// Check if settings exist
	var count int
	err := repo.db.QueryRow("SELECT COUNT(*) FROM cloud_storage_settings").Scan(&count)
	if err != nil {
		logger.Error("Failed to count cloud storage settings: %v", err)
		return err
	}

	if count == 0 {
		// Insert new settings
		_, err = repo.db.Exec(`
			INSERT INTO cloud_storage_settings (
				storage_type, backblaze_key_id, backblaze_app_key,
				backblaze_bucket, backblaze_endpoint, is_enabled,
				created_at, updated_at
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		`, input.StorageType, input.BackblazeKeyID, input.BackblazeAppKey,
			input.BackblazeBucket, input.BackblazeEndpoint, input.IsEnabled,
			now, now)
	} else {
		// Update existing settings
		_, err = repo.db.Exec(`
			UPDATE cloud_storage_settings SET
				storage_type = $1,
				backblaze_key_id = $2,
				backblaze_app_key = $3,
				backblaze_bucket = $4,
				backblaze_endpoint = $5,
				is_enabled = $6,
				updated_at = $7
			WHERE id = (SELECT id FROM cloud_storage_settings ORDER BY id ASC LIMIT 1)
		`, input.StorageType, input.BackblazeKeyID, input.BackblazeAppKey,
			input.BackblazeBucket, input.BackblazeEndpoint, input.IsEnabled, now)
	}

	if err != nil {
		logger.Error("Failed to update cloud storage settings: %v", err)
		return err
	}

	logger.Log("Updated cloud storage settings")
	return nil
}
