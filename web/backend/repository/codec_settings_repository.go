package repository

import (
	"database/sql"
	"showfer-web/models"
	"showfer-web/service/logger"
)

// CodecSettingsRepository handles database operations for codec settings
type CodecSettingsRepository struct {
	db *sql.DB
}

// NewCodecSettingsRepository creates a new CodecSettingsRepository
func NewCodecSettingsRepository(db *sql.DB) *CodecSettingsRepository {
	return &CodecSettingsRepository{
		db: db,
	}
}

// CreateCodecSettingsTable creates the codec_settings table
func CreateCodecSettingsTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS codec_settings (
			id SERIAL PRIMARY KEY,
			vcodec TEXT NOT NULL,
			acodec TEXT NOT NULL,
			resolution TEXT NOT NULL,
			fps REAL NOT NULL,
			sample_rate INTEGER NOT NULL,
			vbitrate INTEGER NOT NULL,
			abitrate INTEGER NOT NULL,
			max_vbitrate INTEGER NOT NULL,
			-- New dual audio fields
			dual_audio_mode BOOLEAN DEFAULT FALSE,
			audio1_codec TEXT DEFAULT 'ac3_passthrough',
			audio1_bitrate INTEGER DEFAULT 448,
			audio1_channels INTEGER DEFAULT 6,
			audio2_codec TEXT DEFAULT 'aac_downmix',
			audio2_bitrate INTEGER DEFAULT 192,
			audio2_channels INTEGER DEFAULT 2
		)
	`)
	if err != nil {
		logger.Error("Failed to create codec_settings table: %v", err)
		return err
	}

	// Check if new columns exist, if not add them (for existing installations)
	var columnExists bool
	err = db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'codec_settings' 
			AND column_name = 'dual_audio_mode'
		)
	`).Scan(&columnExists)
	
	if err == nil && !columnExists {
		// Add new columns for existing installations
		_, err = db.Exec(`
			ALTER TABLE codec_settings 
			ADD COLUMN dual_audio_mode BOOLEAN DEFAULT FALSE,
			ADD COLUMN audio1_codec TEXT DEFAULT 'ac3_passthrough',
			ADD COLUMN audio1_bitrate INTEGER DEFAULT 448,
			ADD COLUMN audio1_channels INTEGER DEFAULT 6,
			ADD COLUMN audio2_codec TEXT DEFAULT 'aac_downmix',
			ADD COLUMN audio2_bitrate INTEGER DEFAULT 192,
			ADD COLUMN audio2_channels INTEGER DEFAULT 2
		`)
		if err != nil {
			logger.Error("Failed to add dual audio columns: %v", err)
			return err
		}
		logger.Log("Added dual audio columns to existing codec_settings table")
	}

	// Check if we need to insert default settings
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM codec_settings").Scan(&count)
	if err != nil {
		logger.Error("Failed to check codec_settings count: %v", err)
		return err
	}

	if count == 0 {
		// Insert default settings
		defaults := models.DefaultCodecSettings()
		_, err = db.Exec(`
			INSERT INTO codec_settings (
				vcodec, acodec, resolution, fps, sample_rate, vbitrate, abitrate, max_vbitrate,
				dual_audio_mode, audio1_codec, audio1_bitrate, audio1_channels,
				audio2_codec, audio2_bitrate, audio2_channels
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
		`,
			defaults.VCodec, defaults.ACodec, defaults.Resolution, defaults.FPS,
			defaults.SampleRate, defaults.VBitrate, defaults.ABitrate, defaults.MaxVBitrate,
			defaults.DualAudioMode, defaults.Audio1Codec, defaults.Audio1Bitrate, defaults.Audio1Channels,
			defaults.Audio2Codec, defaults.Audio2Bitrate, defaults.Audio2Channels,
		)
		if err != nil {
			logger.Error("Failed to insert default codec settings: %v", err)
			return err
		}
	}

	return nil
}

// GetCodecSettings retrieves the codec settings
func (repo *CodecSettingsRepository) GetCodecSettings() (models.CodecSettings, error) {
	var settings models.CodecSettings
	err := repo.db.QueryRow(`
		SELECT id, vcodec, acodec, resolution, fps, sample_rate, vbitrate, abitrate, max_vbitrate,
			   dual_audio_mode, audio1_codec, audio1_bitrate, audio1_channels,
			   audio2_codec, audio2_bitrate, audio2_channels
		FROM codec_settings
		ORDER BY id ASC
		LIMIT 1
	`).Scan(
		&settings.ID, &settings.VCodec, &settings.ACodec, &settings.Resolution, &settings.FPS,
		&settings.SampleRate, &settings.VBitrate, &settings.ABitrate, &settings.MaxVBitrate,
		&settings.DualAudioMode, &settings.Audio1Codec, &settings.Audio1Bitrate, &settings.Audio1Channels,
		&settings.Audio2Codec, &settings.Audio2Bitrate, &settings.Audio2Channels,
	)
	if err != nil {
		logger.Error("Failed to get codec settings: %v", err)
		return models.DefaultCodecSettings(), err
	}

	return settings, nil
}

// UpdateCodecSettings updates the codec settings
func (repo *CodecSettingsRepository) UpdateCodecSettings(input models.CodecSettingsInput) error {
	_, err := repo.db.Exec(`
		UPDATE codec_settings SET
			vcodec = $1, acodec = $2, resolution = $3, fps = $4,
			sample_rate = $5, vbitrate = $6, abitrate = $7, max_vbitrate = $8,
			dual_audio_mode = $9, audio1_codec = $10, audio1_bitrate = $11, audio1_channels = $12,
			audio2_codec = $13, audio2_bitrate = $14, audio2_channels = $15
		WHERE id = 1
	`,
		input.VCodec, input.ACodec, input.Resolution, input.FPS,
		input.SampleRate, input.VBitrate, input.ABitrate, input.MaxVBitrate,
		input.DualAudioMode, input.Audio1Codec, input.Audio1Bitrate, input.Audio1Channels,
		input.Audio2Codec, input.Audio2Bitrate, input.Audio2Channels,
	)
	if err != nil {
		logger.Error("Failed to update codec settings: %v", err)
		return err
	}

	return nil
}
