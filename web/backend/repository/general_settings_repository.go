package repository

import (
	"database/sql"
	"showfer-web/models"
	"showfer-web/service/logger"
)

// GeneralSettingsRepository handles database operations for general settings
type GeneralSettingsRepository struct {
	db *sql.DB
}

// NewGeneralSettingsRepository creates a new GeneralSettingsRepository
func NewGeneralSettingsRepository(db *sql.DB) *GeneralSettingsRepository {
	return &GeneralSettingsRepository{
		db: db,
	}
}

// CreateGeneralSettingsTable creates the general_settings table
func CreateGeneralSettingsTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS general_settings (
			id SERIAL PRIMARY KEY,
			transcoder_threads INTEGER NOT NULL
		)
	`)
	if err != nil {
		logger.Error("Failed to create general_settings table: %v", err)
		return err
	}

	// Check if we need to insert default settings
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM general_settings").Scan(&count)
	if err != nil {
		logger.Error("Failed to check general_settings count: %v", err)
		return err
	}

	if count == 0 {
		// Insert default settings
		defaults := models.DefaultGeneralSettings()
		_, err = db.Exec(`
			INSERT INTO general_settings (
				transcoder_threads
			) VALUES ($1)
		`,
			defaults.TranscoderThreads,
		)
		if err != nil {
			logger.Error("Failed to insert default general settings: %v", err)
			return err
		}
	}

	return nil
}

// GetGeneralSettings retrieves the general settings
func (repo *GeneralSettingsRepository) GetGeneralSettings() (models.GeneralSettings, error) {
	var settings models.GeneralSettings
	err := repo.db.QueryRow(`
		SELECT id, transcoder_threads
		FROM general_settings
		ORDER BY id ASC
		LIMIT 1
	`).Scan(
		&settings.ID, &settings.TranscoderThreads,
	)
	if err != nil {
		logger.Error("Failed to get general settings: %v", err)
		return models.DefaultGeneralSettings(), err
	}

	return settings, nil
}

// UpdateGeneralSettings updates the general settings
func (repo *GeneralSettingsRepository) UpdateGeneralSettings(input models.GeneralSettingsInput) error {
	_, err := repo.db.Exec(`
		UPDATE general_settings SET
			transcoder_threads = $1
		WHERE id = 1
	`,
		input.TranscoderThreads,
	)
	if err != nil {
		logger.Error("Failed to update general settings: %v", err)
		return err
	}

	return nil
}
