package repository

import (
	"database/sql"
	"showfer-web/models"
	"time"
)

type HistoryRepository struct {
	db *sql.DB
}

func NewHistoryRepository(db *sql.DB) *HistoryRepository {
	return &HistoryRepository{db: db}
}

func (r *HistoryRepository) CreateHistory(history models.History) (int64, error) {
	var id int64
	err := r.db.QueryRow(`
		INSERT INTO history
			(created_at, schedule_id, folder, file_id, episode)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id
	`,
		time.Now().UTC().Format(time.RFC3339),
		history.ScheduleId,
		history.Folder,
		history.FileId,
		history.Episode,
	).Scan(&id)

	if err != nil {
		return 0, err
	}

	return id, nil
}

func (r *HistoryRepository) FindAllByScheduleAndFolder(scheduleId int64, path string) ([]models.History, error) {
	var data []models.History

	rows, err := r.db.Query(`
		SELECT folder, file_id, episode FROM history
		WHERE schedule_id = $1 AND folder = $2
	`, scheduleId, path)
	
	if err != nil {
		return data, err
	}
	defer rows.Close()

	for rows.Next() {
		var d models.History

		err = rows.Scan(&d.Folder, &d.FileId, &d.Episode)
		if err != nil {
			return data, err
		}

		data = append(data, d)
	}

	return data, nil
}

func (r *HistoryRepository) DeleteByScheduleAndFolder(scheduleId int64, path string) error {
	_, err := r.db.Exec(`
		DELETE FROM history WHERE schedule_id = $1 AND folder = $2
	`, scheduleId, path)

	return err
}
