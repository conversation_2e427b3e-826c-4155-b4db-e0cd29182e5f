package repository

import (
	"database/sql"
	"showfer-web/models"
	"showfer-web/service/logger"
)

// RecorderRepository handles database operations for recorders
type RecorderRepository struct {
	db *sql.DB
}

// NewRecorderRepository creates a new RecorderRepository
func NewRecorderRepository(db *sql.DB) *RecorderRepository {
	return &RecorderRepository{db: db}
}

// CreateRecorder creates a new recorder
func (r *RecorderRepository) CreateRecorder(recorder models.Recorder) (int64, error) {
	// First create the RTP URL to get its ID
	rtpUrlID, err := r.CreateRtpUrl(recorder.Input, 0) // Use 0 temporarily
	if err != nil {
		logger.Error("Failed to create RTP URL: %v", err)
		return 0, err
	}

	// Now save the recorder with the RTP URL ID
	var id int64
	err = r.db.QueryRow(`
		INSERT INTO recorders (
			name, input, rtp_url_id, duration, status, vcodec, acodec,
			resolution, fps, sample_rate, vbitrate, abitrate, max_vbitrate,
			network_interface, source_ip, service_id, is_scheduled, scheduled_start_time
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
		RETURNING id
	`,
		recorder.Name, recorder.Input, rtpUrlID, recorder.Duration, recorder.Status,
		recorder.VCodec, recorder.ACodec, recorder.Resolution, recorder.FPS,
		recorder.SampleRate, recorder.VBitrate, recorder.ABitrate, recorder.MaxVBitrate,
		recorder.NetworkInterface, recorder.SourceIP, recorder.ServiceID, recorder.IsScheduled, recorder.ScheduledStartTime,
	).Scan(&id)
	if err != nil {
		logger.Error("Failed to create recorder: %v", err)
		// Delete the RTP URL since the recorder creation failed
		_ = r.DeleteRtpUrl(rtpUrlID)
		return 0, err
	}

	// Update the RTP URL with the correct recorder ID
	_, err = r.db.Exec(`
		UPDATE rtp_urls SET
			recorder_id = $1
		WHERE id = $2
	`, id, rtpUrlID)
	if err != nil {
		logger.Error("Failed to update RTP URL with recorder ID: %v", err)
		// Continue even if there's an error updating the RTP URL
	}

	return id, nil
}

// UpdateRecorder updates an existing recorder
func (r *RecorderRepository) UpdateRecorder(recorder models.Recorder) error {
	// First check if there's an existing RTP URL for this recorder
	var existingRtpUrlID sql.NullInt64 // Use sql.NullInt64 to handle NULL values
	err := r.db.QueryRow("SELECT rtp_url_id FROM recorders WHERE id = $1", recorder.ID).Scan(&existingRtpUrlID)
	if err != nil && err != sql.ErrNoRows {
		logger.Error("Failed to get existing RTP URL ID: %v", err)
		return err
	}

	// Create or update the RTP URL
	rtpUrlID, err := r.CreateRtpUrl(recorder.Input, int(recorder.ID))
	if err != nil {
		logger.Error("Failed to create/update RTP URL: %v", err)
		return err
	}

	// Now update the recorder with the new RTP URL ID
	_, err = r.db.Exec(`
		UPDATE recorders SET
			name=$1, input=$2, rtp_url_id=$3, duration=$4, status=$5, vcodec=$6, acodec=$7,
			resolution=$8, fps=$9, sample_rate=$10, vbitrate=$11, abitrate=$12, max_vbitrate=$13,
			network_interface=$14, source_ip=$15, service_id=$16, is_scheduled=$17, scheduled_start_time=$18, updated_at=NOW()
		WHERE id=$19
	`,
		recorder.Name, recorder.Input, rtpUrlID, recorder.Duration, recorder.Status,
		recorder.VCodec, recorder.ACodec, recorder.Resolution, recorder.FPS,
		recorder.SampleRate, recorder.VBitrate, recorder.ABitrate, recorder.MaxVBitrate,
		recorder.NetworkInterface, recorder.SourceIP, recorder.ServiceID, recorder.IsScheduled, recorder.ScheduledStartTime, recorder.ID,
	)
	if err != nil {
		logger.Error("Failed to update recorder: %v", err)
		return err
	}

	// If the RTP URL ID has changed, delete the old one if it's not being used
	if existingRtpUrlID.Valid && existingRtpUrlID.Int64 > 0 && existingRtpUrlID.Int64 != rtpUrlID {
		inUse, err := r.IsRtpUrlInUse(existingRtpUrlID.Int64)
		if err != nil {
			logger.Error("Failed to check if old RTP URL is in use: %v", err)
			// Continue even if there's an error checking if the old RTP URL is in use
		} else if !inUse {
			err = r.DeleteRtpUrl(existingRtpUrlID.Int64)
			if err != nil {
				logger.Error("Failed to delete old RTP URL: %v", err)
				// Continue even if there's an error deleting the old RTP URL
			}
		}
	}

	return nil
}

// GetRecorders gets all recorders with pagination
func (r *RecorderRepository) GetRecorders(pagination models.Pagination) (models.RecorderListResult, error) {
	// Calculate offset
	offset := (pagination.Page - 1) * pagination.Limit

	// Get total count
	var totalItems int
	err := r.db.QueryRow("SELECT COUNT(*) FROM recorders").Scan(&totalItems)
	if err != nil {
		logger.Error("Failed to get total recorder count: %v", err)
		return models.RecorderListResult{}, err
	}

	// Get recorders with pagination
	rows, err := r.db.Query(`
		SELECT id, name, input, rtp_url_id, duration, status, vcodec, acodec,
			resolution, fps, sample_rate, vbitrate, abitrate, max_vbitrate,
			network_interface, source_ip, service_id, is_scheduled, scheduled_start_time, created_at, updated_at
		FROM recorders
		ORDER BY id DESC
		LIMIT $1 OFFSET $2
	`, pagination.Limit, offset)
	if err != nil {
		logger.Error("Failed to get recorders: %v", err)
		return models.RecorderListResult{}, err
	}
	defer rows.Close()

	var recorders []models.Recorder
	for rows.Next() {
		var recorder models.Recorder
		var rtpUrlID sql.NullInt64 // Use NullInt64 to handle NULL values
		var scheduledStartTime sql.NullString
		var createdAt, updatedAt sql.NullString

		err := rows.Scan(
			&recorder.ID, &recorder.Name, &recorder.Input, &rtpUrlID, &recorder.Duration,
			&recorder.Status, &recorder.VCodec, &recorder.ACodec, &recorder.Resolution,
			&recorder.FPS, &recorder.SampleRate, &recorder.VBitrate, &recorder.ABitrate,
			&recorder.MaxVBitrate, &recorder.NetworkInterface, &recorder.SourceIP, &recorder.ServiceID,
			&recorder.IsScheduled, &scheduledStartTime, &createdAt, &updatedAt,
		)
		if err != nil {
			logger.Error("Failed to scan recorder: %v", err)
			continue
		}

		// Set the RtpUrlID if it's not NULL
		if rtpUrlID.Valid {
			recorder.RtpUrlID = rtpUrlID.Int64
		}

		// Set the ScheduledStartTime if it's not NULL
		if scheduledStartTime.Valid {
			recorder.ScheduledStartTime = &scheduledStartTime.String
		}

		// Set timestamps
		if createdAt.Valid {
			recorder.CreatedAt = createdAt.String
		}
		if updatedAt.Valid {
			recorder.UpdatedAt = updatedAt.String
		}

		recorders = append(recorders, recorder)
	}

	// Calculate total pages
	totalPages := totalItems / pagination.Limit
	if totalItems%pagination.Limit > 0 {
		totalPages++
	}

	return models.RecorderListResult{
		Items:      recorders,
		TotalItems: totalItems,
		TotalPages: totalPages,
		Page:       pagination.Page,
		Limit:      pagination.Limit,
	}, nil
}

// GetRecorderByID gets a recorder by ID
func (r *RecorderRepository) GetRecorderByID(id int) (models.Recorder, error) {
	var recorder models.Recorder
	var rtpUrlID sql.NullInt64 // Use NullInt64 to handle NULL values
	var scheduledStartTime sql.NullString
	var createdAt, updatedAt sql.NullString

	err := r.db.QueryRow(`
		SELECT id, name, input, rtp_url_id, duration, status, vcodec, acodec,
			resolution, fps, sample_rate, vbitrate, abitrate, max_vbitrate,
			network_interface, source_ip, service_id, is_scheduled, scheduled_start_time, created_at, updated_at
		FROM recorders
		WHERE id = $1
	`, id).Scan(
		&recorder.ID, &recorder.Name, &recorder.Input, &rtpUrlID, &recorder.Duration,
		&recorder.Status, &recorder.VCodec, &recorder.ACodec, &recorder.Resolution,
		&recorder.FPS, &recorder.SampleRate, &recorder.VBitrate, &recorder.ABitrate,
		&recorder.MaxVBitrate, &recorder.NetworkInterface, &recorder.SourceIP, &recorder.ServiceID,
		&recorder.IsScheduled, &scheduledStartTime, &createdAt, &updatedAt,
	)
	if err != nil {
		logger.Error("Failed to get recorder by ID: %v", err)
		return models.Recorder{}, err
	}

	// Set the RtpUrlID if it's not NULL
	if rtpUrlID.Valid {
		recorder.RtpUrlID = rtpUrlID.Int64
	}

	// Set the ScheduledStartTime if it's not NULL
	if scheduledStartTime.Valid {
		recorder.ScheduledStartTime = &scheduledStartTime.String
	}

	// Set timestamps
	if createdAt.Valid {
		recorder.CreatedAt = createdAt.String
	}
	if updatedAt.Valid {
		recorder.UpdatedAt = updatedAt.String
	}

	return recorder, nil
}

// DeleteRecorder deletes a recorder by ID
func (r *RecorderRepository) DeleteRecorder(id int) error {
	// Delete the recorder
	_, err := r.db.Exec("DELETE FROM recorders WHERE id = $1", id)
	if err != nil {
		logger.Error("Failed to delete recorder: %v", err)
		return err
	}

	// Delete all RTP URLs associated with this recorder
	err = r.DeleteRtpUrlsByRecorderId(id)
	if err != nil {
		logger.Error("Failed to delete RTP URLs for recorder %d: %v", id, err)
		// Continue even if there's an error deleting the RTP URLs
	}

	return nil
}

// StartRecorder updates a recorder's status to 'running'
func (r *RecorderRepository) StartRecorder(id int) error {
	_, err := r.db.Exec("UPDATE recorders SET status = 'running' WHERE id = $1", id)
	if err != nil {
		logger.Error("Failed to start recorder: %v", err)
	}
	return err
}

// StopRecorder updates a recorder's status to 'stopped'
func (r *RecorderRepository) StopRecorder(id int) error {
	_, err := r.db.Exec("UPDATE recorders SET status = 'stopped' WHERE id = $1", id)
	if err != nil {
		logger.Error("Failed to stop recorder: %v", err)
	}
	return err
}

// CompleteRecorder updates a recorder's status to 'completed'
func (r *RecorderRepository) CompleteRecorder(id int) error {
	_, err := r.db.Exec("UPDATE recorders SET status = 'completed' WHERE id = $1", id)
	if err != nil {
		logger.Error("Failed to complete recorder: %v", err)
	}
	return err
}

// ResetScheduling resets the scheduling fields for a recorder when a scheduled recording completes
func (r *RecorderRepository) ResetScheduling(id int) error {
	_, err := r.db.Exec("UPDATE recorders SET is_scheduled = FALSE, scheduled_start_time = NULL WHERE id = $1", id)
	if err != nil {
		logger.Error("Failed to reset scheduling for recorder: %v", err)
	}
	return err
}

// UpdateRecorderRecordingParams updates the service_id and source_ip when recording starts
func (r *RecorderRepository) UpdateRecorderRecordingParams(id int, serviceID int, sourceIP string) error {
	// Convert serviceID to pointer for database (NULL if 0)
	var serviceIDPtr *int
	if serviceID > 0 {
		serviceIDPtr = &serviceID
	}

	_, err := r.db.Exec("UPDATE recorders SET service_id = $1, source_ip = $2 WHERE id = $3", 
		serviceIDPtr, sourceIP, id)
	if err != nil {
		logger.Error("Failed to update recorder recording params: %v", err)
	}
	return err
}

// FailRecorder updates a recorder's status to 'failed'
func (r *RecorderRepository) FailRecorder(id int) error {
	_, err := r.db.Exec("UPDATE recorders SET status = 'failed' WHERE id = $1", id)
	if err != nil {
		logger.Error("Failed to mark recorder as failed: %v", err)
	}
	return err
}

// CreateRtpUrl creates a new RTP URL or returns an existing one
func (r *RecorderRepository) CreateRtpUrl(url string, recorderID int) (int64, error) {
	// Check if URL already exists for this recorder
	var id int64
	err := r.db.QueryRow("SELECT id FROM rtp_urls WHERE url = $1 AND recorder_id = $2", url, recorderID).Scan(&id)
	if err == nil {
		// URL already exists for this recorder, return its ID
		return id, nil
	}

	// URL doesn't exist for this recorder, create a new one
	err = r.db.QueryRow(`
		INSERT INTO rtp_urls (url, recorder_id, updated_at)
		VALUES ($1, $2, NOW())
		RETURNING id
	`, url, recorderID).Scan(&id)
	if err != nil {
		logger.Error("Failed to create RTP URL: %v", err)
		return 0, err
	}

	return id, nil
}

// DeleteRtpUrl deletes an RTP URL by ID
func (r *RecorderRepository) DeleteRtpUrl(id int64) error {
	_, err := r.db.Exec("DELETE FROM rtp_urls WHERE id = $1", id)
	if err != nil {
		logger.Error("Failed to delete RTP URL: %v", err)
		return err
	}
	return nil
}

// DeleteRtpUrlsByRecorderId deletes all RTP URLs for a recorder
func (r *RecorderRepository) DeleteRtpUrlsByRecorderId(recorderID int) error {
	_, err := r.db.Exec("DELETE FROM rtp_urls WHERE recorder_id = $1", recorderID)
	if err != nil {
		logger.Error("Failed to delete RTP URLs for recorder %d: %v", recorderID, err)
		return err
	}
	return nil
}

// IsRtpUrlInUse checks if an RTP URL is in use by any recorder
func (r *RecorderRepository) IsRtpUrlInUse(id int64) (bool, error) {
	var count int
	err := r.db.QueryRow("SELECT COUNT(*) FROM recorders WHERE rtp_url_id = $1", id).Scan(&count)
	if err != nil {
		logger.Error("Failed to check if RTP URL is in use: %v", err)
		return false, err
	}
	return count > 0, nil
}

// GetAllRtpUrls gets all RTP URLs
func (r *RecorderRepository) GetAllRtpUrls() ([]models.RtpUrl, error) {
	rows, err := r.db.Query(`
		SELECT id, url, recorder_id, created_at, updated_at
		FROM rtp_urls
		ORDER BY updated_at DESC
	`)
	if err != nil {
		logger.Error("Failed to get RTP URLs: %v", err)
		return nil, err
	}
	defer rows.Close()

	var rtpUrls []models.RtpUrl
	for rows.Next() {
		var rtpUrl models.RtpUrl
		err := rows.Scan(&rtpUrl.ID, &rtpUrl.URL, &rtpUrl.RecorderID, &rtpUrl.CreatedAt, &rtpUrl.UpdatedAt)
		if err != nil {
			logger.Error("Failed to scan RTP URL row: %v", err)
			continue
		}
		rtpUrls = append(rtpUrls, rtpUrl)
	}

	return rtpUrls, nil
}
