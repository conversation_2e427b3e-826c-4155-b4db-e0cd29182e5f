package repository

import (
	"database/sql"
	"fmt"
	"os/exec"
	"showfer-web/models"
	"showfer-web/service/logger"
	"strconv"
	"strings"
)

// NetworkRepository handles operations related to network interfaces
type NetworkRepository struct {
	db *sql.DB
}

// NewNetworkRepository creates a new NetworkRepository
func NewNetworkRepository(db *sql.DB) *NetworkRepository {
	return &NetworkRepository{
		db: db,
	}
}

// GetNetworkInterfaces retrieves all network interfaces
// Note: This returns ALL interfaces (including unconfigured ones)
// For operational use (binding/capturing), use the recorder API endpoint which filters to only active interfaces with IP addresses
// For administrative use (configuration), use the admin API endpoint which shows all interfaces
func (r *NetworkRepository) GetNetworkInterfaces() ([]models.NetworkInterface, error) {
	// First, get all network interfaces (including those without IP addresses)
	linkCmd := exec.Command("ip", "link", "show")
	linkOutput, err := linkCmd.Output()
	if err != nil {
		logger.Error("Failed to get network interfaces: %v", err)
		return nil, err
	}

	// Parse the output
	var interfaces []models.NetworkInterface
	interfaceMap := make(map[string]*models.NetworkInterface)

	// Parse link output to get all interfaces
	linkLines := strings.Split(string(linkOutput), "\n")
	for _, line := range linkLines {
		if line == "" {
			continue
		}

		// Look for interface lines (they start with a number and colon)
		if strings.Contains(line, ": ") && !strings.HasPrefix(line, " ") {
			fields := strings.Fields(line)
			if len(fields) < 2 {
				continue
			}

			// Extract interface name (remove colon and any @ suffix)
			nameField := fields[1]
			nameField = strings.TrimSuffix(nameField, ":")
			name := strings.Split(nameField, "@")[0] // Remove @veth... suffixes

			// Skip loopback and Docker interfaces
			if name == "lo" || 
			   strings.HasPrefix(name, "docker") || 
			   strings.HasPrefix(name, "br-") || 
			   strings.HasPrefix(name, "veth") {
				continue
			}

			// Get MAC address from this line or the next line
			macAddress := ""
			if strings.Contains(line, "link/ether") {
				// MAC is on the same line
				for i, field := range fields {
					if field == "link/ether" && i+1 < len(fields) {
						macAddress = fields[i+1]
						break
					}
				}
			}

			// Check if interface is wireless
			isWireless := false
			wirelessCmd := exec.Command("iwconfig", name)
			wirelessOutput, err := wirelessCmd.Output()
			if err == nil {
				// Check if the output contains wireless extensions
				wirelessOutputStr := string(wirelessOutput)
				isWireless = !strings.Contains(wirelessOutputStr, "no wireless extensions")
			}

			// Create interface entry with default values
			interfaceMap[name] = &models.NetworkInterface{
				Name:       name,
				IPAddress:  "",
				Netmask:    "",
				Gateway:    "",
				DNSServers: "",
				IsWireless: isWireless,
				MacAddress: macAddress,
				IsActive:   true, // Show all interfaces as active by default
			}
		}
	}

	// Now get IP address information
	cmd := exec.Command("ip", "-o", "addr", "show")
	output, err := cmd.Output()
	if err != nil {
		logger.Error("Failed to get network interface addresses: %v", err)
		return nil, err
	}

	// Update interfaces with IP address information
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if line == "" {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) < 4 {
			continue
		}

		// Extract interface name
		name := fields[1]

		// Skip if interface doesn't exist in our map (shouldn't happen)
		iface, exists := interfaceMap[name]
		if !exists {
			continue
		}

		// Extract IP address and netmask (specifically IPv4)
		ipInfo := ""
		for i, field := range fields {
			// Look for IPv4 addresses - need to check for both "inet" and "inet4"
			if (field == "inet" || field == "inet4" || strings.HasPrefix(field, "inet/")) && i+1 < len(fields) {
				ipInfo = fields[i+1]
				break
			}
		}

		if ipInfo != "" {
			parts := strings.Split(ipInfo, "/")
			if len(parts) == 2 {
				ipAddress := parts[0]

				// Convert CIDR to dotted decimal format for better readability
				cidrPrefix, err := strconv.Atoi(parts[1])
				netmask := ""
				if err == nil && cidrPrefix >= 0 && cidrPrefix <= 32 {
					// Convert CIDR to dotted decimal
					var mask uint32 = 0xffffffff
					if cidrPrefix < 32 {
						mask = mask << (32 - cidrPrefix)
					}
					// Format as dotted decimal
					netmask = fmt.Sprintf("%d.%d.%d.%d",
						(mask>>24)&0xff,
						(mask>>16)&0xff,
						(mask>>8)&0xff,
						mask&0xff)
				} else {
					// Keep as CIDR if conversion fails
					netmask = parts[1]
				}

				// Update interface with IP information
				iface.IPAddress = ipAddress
				iface.Netmask = netmask
			}
		}
	}

	// Get gateway and DNS information for each interface
	for _, iface := range interfaceMap {
		name := iface.Name

		// Get gateway - first try interface-specific default route
		gateway := ""
		gatewayCmd := exec.Command("ip", "route", "show", "dev", name)
		gatewayOutput, err := gatewayCmd.Output()
		if err == nil {
			gatewayLines := strings.Split(string(gatewayOutput), "\n")
			for _, gatewayLine := range gatewayLines {
				if strings.Contains(gatewayLine, "default via") {
					gatewayFields := strings.Fields(gatewayLine)
					if len(gatewayFields) > 2 {
						gateway = gatewayFields[2]
					}
					break
				}
			}
		}

		// If no gateway found, try the global default route
		if gateway == "" {
			defaultRouteCmd := exec.Command("ip", "route", "show", "default")
			defaultRouteOutput, err := defaultRouteCmd.Output()
			if err == nil {
				defaultRouteLines := strings.Split(string(defaultRouteOutput), "\n")
				for _, routeLine := range defaultRouteLines {
					if strings.Contains(routeLine, "default via") && strings.Contains(routeLine, "dev "+name) {
						routeFields := strings.Fields(routeLine)
						if len(routeFields) > 2 {
							gateway = routeFields[2]
						}
						break
					}
				}
			}
		}

		iface.Gateway = gateway

		// Get DNS servers - first try systemd-resolved
		dnsServers := ""
		dnsServerList := []string{}

		// Try systemd-resolved first (modern Linux systems)
		resolvectlCmd := exec.Command("resolvectl", "status")
		resolvectlOutput, err := resolvectlCmd.Output()
		if err == nil {
			resolvectlLines := strings.Split(string(resolvectlOutput), "\n")
			for _, line := range resolvectlLines {
				line = strings.TrimSpace(line)
				if strings.HasPrefix(line, "DNS Servers:") {
					dnsInfo := strings.TrimPrefix(line, "DNS Servers:")
					dnsInfo = strings.TrimSpace(dnsInfo)
					servers := strings.Split(dnsInfo, " ")
					for _, server := range servers {
						server = strings.TrimSpace(server)
						if server != "" && !strings.Contains(server, ":") { // Filter out IPv6 addresses
							dnsServerList = append(dnsServerList, server)
						}
					}
					break
				}
			}
		}

		// If no DNS servers found, fall back to /etc/resolv.conf
		if len(dnsServerList) == 0 {
			dnsCmd := exec.Command("cat", "/etc/resolv.conf")
			dnsOutput, err := dnsCmd.Output()
			if err == nil {
				dnsLines := strings.Split(string(dnsOutput), "\n")
				for _, dnsLine := range dnsLines {
					if strings.HasPrefix(dnsLine, "nameserver") {
						dnsFields := strings.Fields(dnsLine)
						if len(dnsFields) > 1 && dnsFields[1] != "**********" { // Skip systemd-resolved stub
							dnsServerList = append(dnsServerList, dnsFields[1])
						}
					}
				}
			}
		}

		// If still no DNS servers found, use Google's public DNS as fallback
		if len(dnsServerList) == 0 {
			dnsServerList = append(dnsServerList, "*******", "*******")
		}

		dnsServers = strings.Join(dnsServerList, ",")
		iface.DNSServers = dnsServers
	}

	// Convert map to slice
	interfaces = []models.NetworkInterface{}
	for _, iface := range interfaceMap {
		interfaces = append(interfaces, *iface)
	}

	return interfaces, nil
}

// UpdateNetworkInterface updates a network interface configuration
func (r *NetworkRepository) UpdateNetworkInterface(name string, input models.NetworkInterfaceInput) error {
	// Get current interfaces to check if the interface exists
	interfaces, err := r.GetNetworkInterfaces()
	if err != nil {
		return err
	}

	interfaceExists := false
	for _, iface := range interfaces {
		if iface.Name == name {
			interfaceExists = true
			break
		}
	}

	if !interfaceExists {
		return logger.ErrorWithReturn("Network interface not found: %s", name)
	}

	// First, bring the interface up to ensure it's ready for configuration
	upCmd := exec.Command("ip", "link", "set", name, "up")
	output, err := upCmd.CombinedOutput()
	if err != nil {
		errorMsg := string(output)
		if errorMsg == "" {
			errorMsg = err.Error()
		}

		// Check for permission errors
		if strings.Contains(errorMsg, "permission denied") || err.Error() == "exit status 255" {
			return logger.ErrorWithReturn("Permission denied: Network configuration requires administrator privileges. Please run the application with sudo or as root.")
		}

		return logger.ErrorWithReturn("Failed to bring interface up: %v - %s", err, errorMsg)
	}

	logger.Log("Brought interface %s up successfully", name)

	// Set IP address and netmask
	ipCmd := exec.Command("ip", "addr", "flush", "dev", name)
	output1, err1 := ipCmd.CombinedOutput()
	if err1 != nil {
		errorMsg := string(output1)
		if errorMsg == "" {
			errorMsg = err1.Error()
		}

		// Check for permission errors
		if strings.Contains(errorMsg, "permission denied") || err1.Error() == "exit status 255" {
			return logger.ErrorWithReturn("Permission denied: Network configuration requires administrator privileges. Please run the application with sudo or as root.")
		}

		return logger.ErrorWithReturn("Failed to flush IP address: %v - %s", err1, errorMsg)
	}

	// Convert dotted decimal netmask to CIDR if needed
	netmask := input.Netmask
	if strings.Contains(netmask, ".") {
		// Convert dotted decimal to CIDR
		parts := strings.Split(netmask, ".")
		if len(parts) == 4 {
			// Parse each octet
			var octets [4]uint8
			for i, part := range parts {
				val, errConv := strconv.Atoi(part)
				if errConv != nil || val < 0 || val > 255 {
					return logger.ErrorWithReturn("Invalid netmask format: %s", netmask)
				}
				octets[i] = uint8(val)
			}

			// Calculate CIDR prefix
			bits := uint32(octets[0])<<24 | uint32(octets[1])<<16 | uint32(octets[2])<<8 | uint32(octets[3])
			cidr := 0
			for i := 0; i < 32; i++ {
				if bits&(1<<(31-i)) != 0 {
					cidr++
				} else {
					break
				}
			}
			netmask = fmt.Sprintf("%d", cidr)
		} else {
			return logger.ErrorWithReturn("Invalid netmask format: %s", netmask)
		}
	}

	ipCmd = exec.Command("ip", "addr", "add", input.IPAddress+"/"+netmask, "dev", name)
	output2, err2 := ipCmd.CombinedOutput()
	if err2 != nil {
		errorMsg := string(output2)
		if errorMsg == "" {
			errorMsg = err2.Error()
		}

		// Check for permission errors
		if strings.Contains(errorMsg, "permission denied") || err2.Error() == "exit status 255" {
			return logger.ErrorWithReturn("Permission denied: Network configuration requires administrator privileges. Please run the application with sudo or as root.")
		}

		return logger.ErrorWithReturn("Failed to set IP address: %v - %s", err2, errorMsg)
	}

	// Remove any existing default routes for this interface to prevent conflicts
	delRouteCmd := exec.Command("ip", "route", "del", "default", "dev", name)
	var delRouteOutput []byte
	delRouteOutput, _ = delRouteCmd.CombinedOutput() // Ignore errors as route might not exist
	if len(delRouteOutput) > 0 {
		logger.Log("Removed existing default route for interface %s", name)
	}

	// Set gateway
	routeCmd := exec.Command("ip", "route", "add", "default", "via", input.Gateway, "dev", name)
	output3, err3 := routeCmd.CombinedOutput()
	if err3 != nil {
		errorMsg := string(output3)
		if errorMsg == "" {
			errorMsg = err3.Error()
		}

		// Check for permission errors
		if strings.Contains(errorMsg, "permission denied") || err3.Error() == "exit status 255" {
			return logger.ErrorWithReturn("Permission denied: Network configuration requires administrator privileges. Please run the application with sudo or as root.")
		}

		return logger.ErrorWithReturn("Failed to set gateway: %v - %s", err3, errorMsg)
	}

	// Set DNS servers
	dnsServers := strings.Split(input.DNSServers, ",")
	resolvConfContent := "# Generated by Showfer Web\n"
	for _, dnsServer := range dnsServers {
		resolvConfContent += "nameserver " + strings.TrimSpace(dnsServer) + "\n"
	}

	// Write to resolv.conf
	cmd := exec.Command("bash", "-c", "echo '"+resolvConfContent+"' > /etc/resolv.conf")
	output4, err4 := cmd.CombinedOutput()
	if err4 != nil {
		errorMsg := string(output4)
		if errorMsg == "" {
			errorMsg = err4.Error()
		}

		// Check for permission errors
		if strings.Contains(errorMsg, "permission denied") || err4.Error() == "exit status 255" || strings.Contains(errorMsg, "Permission denied") {
			return logger.ErrorWithReturn("Permission denied: Modifying DNS settings requires administrator privileges. Please run the application with sudo or as root.")
		}

		return logger.ErrorWithReturn("Failed to set DNS servers: %v - %s", err4, errorMsg)
	}

	// Verify the interface is up and log the final status
	statusCmd := exec.Command("ip", "link", "show", name)
	statusOutput, err5 := statusCmd.Output()
	if err5 == nil {
		statusStr := string(statusOutput)
		if strings.Contains(statusStr, "state UP") || strings.Contains(statusStr, "UP,LOWER_UP") {
			logger.Log("Interface %s is up and configured successfully", name)
		} else {
			logger.Log("Interface %s configured but may not be fully up: %s", name, statusStr)
		}
	}

	return nil
}
