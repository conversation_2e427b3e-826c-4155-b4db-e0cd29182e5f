package repository

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"math/rand"
	"showfer-web/models"
	"time"
)

type ScheduleRepository struct {
	db *sql.DB
}

func NewScheduleRepository(db *sql.DB) *ScheduleRepository {
	return &ScheduleRepository{db: db}
}

func (r *ScheduleRepository) CreateSchedule(schedule models.Schedule) (int64, error) {
	var id int64
	err := r.db.QueryRow(`
		INSERT INTO schedules
		    (name, icon, timezone, short_id, output_url, network_interface, ads, channels, regular_days, special_days, fillers, autosave)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		RETURNING id
	`,
		schedule.Name,
		schedule.Icon,
		schedule.Timezone,
		schedule.ShortID,
		schedule.OutputUrl,
		schedule.NetworkInterface,
		r.objectTo<PERSON><PERSON>(schedule.Ads),
		r.object<PERSON><PERSON><PERSON><PERSON>(schedule.Channels),
		r.object<PERSON><PERSON><PERSON><PERSON>(schedule.RegularDays),
		r.object<PERSON><PERSON><PERSON><PERSON>(schedule.SpecialDays),
		r.object<PERSON><PERSON><PERSON><PERSON>(schedule.Fillers),
		schedule.Autosave,
	).Scan(&id)

	if err != nil {
		return 0, err
	}

	return id, nil
}

func (r *ScheduleRepository) UpdateSchedule(schedule models.Schedule) error {
	_, err := r.db.Exec(`
		UPDATE schedules SET updated_at = $1, name = $2, icon = $3, timezone = $4, output_url = $5, network_interface = $6, ads = $7, channels = $8, regular_days = $9, special_days = $10, fillers = $11, autosave = $12
		WHERE id = $13
	`,
		time.Now().Format(time.RFC3339),
		schedule.Name,
		schedule.Icon,
		schedule.Timezone,
		schedule.OutputUrl,
		schedule.NetworkInterface,
		r.objectToJson(schedule.Ads),
		r.objectToJson(schedule.Channels),
		r.objectToJson(schedule.RegularDays),
		r.objectToJson(schedule.SpecialDays),
		r.objectToJson(schedule.Fillers),
		schedule.Autosave,
		schedule.ID,
	)

	return err
}

func (r *ScheduleRepository) DeleteSchedule(id int) error {
	// First, delete all analytics records for this schedule to avoid foreign key constraint violations
	analyticsRepo := NewAnalyticsRepository(r.db)
	err := analyticsRepo.DeleteAnalyticsForSchedule(int64(id))
	if err != nil {
		return fmt.Errorf("failed to delete analytics for schedule: %w", err)
	}

	// Now delete the schedule
	_, err = r.db.Exec(`DELETE FROM schedules WHERE id = $1`, id)
	return err
}

func (r *ScheduleRepository) FindById(id int) (models.Schedule, error) {
	row := r.db.QueryRow(`
		SELECT id, created_at, updated_at, name, icon, timezone, short_id, output_url, network_interface, ads, channels, regular_days, special_days, fillers, autosave
		FROM schedules
		WHERE id = $1
	`, id)

	var d models.Schedule
	var ads string
	var channels string
	var regularDays string
	var specialDays string
	var fillers string

	err := row.Scan(&d.ID, &d.CreatedAt, &d.UpdatedAt, &d.Name, &d.Icon, &d.Timezone, &d.ShortID, &d.OutputUrl, &d.NetworkInterface, &ads, &channels, &regularDays, &specialDays, &fillers, &d.Autosave)
	if err != nil {
		return models.Schedule{}, err
	}
	if err = json.Unmarshal([]byte(ads), &d.Ads); err != nil {
		return models.Schedule{}, err
	}
	if err = json.Unmarshal([]byte(channels), &d.Channels); err != nil {
		return models.Schedule{}, err
	}
	if err = json.Unmarshal([]byte(regularDays), &d.RegularDays); err != nil {
		return models.Schedule{}, err
	}
	if err = json.Unmarshal([]byte(specialDays), &d.SpecialDays); err != nil {
		return models.Schedule{}, err
	}
	if err = json.Unmarshal([]byte(fillers), &d.Fillers); err != nil {
		return models.Schedule{}, err
	}

	return d, nil
}

func (r *ScheduleRepository) ListSchedules(pagination models.Pagination) (models.ScheduleListResult, error) {
	var total int
	err := r.db.QueryRow(`SELECT COUNT(*) FROM schedules`).Scan(&total)
	if err != nil {
		return models.ScheduleListResult{}, err
	}
	if total == 0 {
		return models.ScheduleListResult{}, nil
	}

	rows, err := r.db.Query(`
		SELECT id, updated_at, name, icon, timezone, short_id, output_url, network_interface, ads, channels, regular_days, special_days, fillers, autosave
		FROM schedules
		ORDER BY updated_at DESC
		LIMIT $1
		OFFSET $2
	`, pagination.Limit, (pagination.Page-1)*pagination.Limit)

	if err != nil {
		return models.ScheduleListResult{}, err
	}
	defer rows.Close()

	var items []models.Schedule
	for rows.Next() {
		var d models.Schedule
		var ads string
		var channels string
		var regularDays string
		var specialDays string
		var fillers string

		err = rows.Scan(&d.ID, &d.UpdatedAt, &d.Name, &d.Icon, &d.Timezone, &d.ShortID, &d.OutputUrl, &d.NetworkInterface, &ads, &channels, &regularDays, &specialDays, &fillers, &d.Autosave)
		if err != nil {
			return models.ScheduleListResult{}, err
		}
		if err = json.Unmarshal([]byte(ads), &d.Ads); err != nil {
			return models.ScheduleListResult{}, err
		}
		if err = json.Unmarshal([]byte(channels), &d.Channels); err != nil {
			return models.ScheduleListResult{}, err
		}
		if err = json.Unmarshal([]byte(regularDays), &d.RegularDays); err != nil {
			return models.ScheduleListResult{}, err
		}
		if err = json.Unmarshal([]byte(specialDays), &d.SpecialDays); err != nil {
			return models.ScheduleListResult{}, err
		}
		if err = json.Unmarshal([]byte(fillers), &d.Fillers); err != nil {
			return models.ScheduleListResult{}, err
		}

		items = append(items, d)
	}

	totalPages := total / pagination.Limit
	if total%pagination.Limit > 0 {
		totalPages++
	}

	return models.ScheduleListResult{
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
		Page:       pagination.Page,
		Limit:      pagination.Limit,
	}, nil
}

func (r *ScheduleRepository) UpdateScheduleShortId(scheduleID int64) (string, error) {
	shortID := r.generateShortID(5)

	_, err := r.db.Exec(`
		UPDATE schedules SET short_id = $1
		WHERE id = $2
	`,
		shortID,
		scheduleID,
	)

	return shortID, err
}

func (r *ScheduleRepository) generateShortID(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
	result := make([]byte, length)

	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}

func (r *ScheduleRepository) objectToJson(object any) string {
	jsonData, err := json.Marshal(object)
	if err != nil {
		panic(err)
	}

	return string(jsonData)
}
