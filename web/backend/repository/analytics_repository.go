package repository

import (
	"context"
	"database/sql"
	"errors"
	"showfer-web/models"
	"showfer-web/service/logger"
	"strings"
	"time"
)

// AnalyticsRepository handles database operations for content analytics
type AnalyticsRepository struct {
	db *sql.DB
}

// NewAnalyticsRepository creates a new AnalyticsRepository
func NewAnalyticsRepository(db *sql.DB) *AnalyticsRepository {
	return &AnalyticsRepository{db: db}
}

// RecordContentPlay records a content playback event
func (r *AnalyticsRepository) RecordContentPlay(analytics models.ContentAnalytics) (int64, error) {
	var id int64
	err := r.db.QueryRow(`
		INSERT INTO content_analytics
			(schedule_id, item_id, content_name, content_path, rtp_output, played_at, duration, play_type)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id
	`,
		analytics.ScheduleID,
		analytics.ItemID,
		analytics.ContentName,
		analytics.ContentPath,
		analytics.RTPOutput,
		analytics.PlayedAt,
		analytics.Duration,
		analytics.PlayType,
	).Scan(&id)

	if err != nil {
		logger.Error("Failed to record content play: %v", err)
		return 0, err
	}

	return id, nil
}

// GetContentAnalytics retrieves content analytics with pagination
func (r *AnalyticsRepository) GetContentAnalytics(scheduleID int64, pagination models.Pagination) (models.ContentAnalyticsListResult, error) {
	// Calculate offset
	offset := (pagination.Page - 1) * pagination.Limit

	// Get total count
	var totalItems int
	var err error
	if scheduleID > 0 {
		err = r.db.QueryRow("SELECT COUNT(*) FROM content_analytics WHERE schedule_id = $1", scheduleID).Scan(&totalItems)
	} else {
		err = r.db.QueryRow("SELECT COUNT(*) FROM content_analytics").Scan(&totalItems)
	}

	if err != nil {
		logger.Error("Failed to get total analytics count: %v", err)
		return models.ContentAnalyticsListResult{}, err
	}

	// Prepare query
	var rows *sql.Rows
	if scheduleID > 0 {
		rows, err = r.db.Query(`
			SELECT id, schedule_id, item_id, content_name, content_path, rtp_output, played_at, duration, play_type
			FROM content_analytics
			WHERE schedule_id = $1
			ORDER BY played_at DESC
			LIMIT $2 OFFSET $3
	`, scheduleID, pagination.Limit, offset)
	} else {
		rows, err = r.db.Query(`
			SELECT id, schedule_id, item_id, content_name, content_path, rtp_output, played_at, duration, play_type
			FROM content_analytics
			ORDER BY played_at DESC
					LIMIT $1 OFFSET $2
	`, pagination.Limit, offset)
	}

	if err != nil {
		logger.Error("Failed to get content analytics: %v", err)
		return models.ContentAnalyticsListResult{}, err
	}
	defer rows.Close()

	var items []models.ContentAnalytics
	for rows.Next() {
		var analytics models.ContentAnalytics
		var itemID sql.NullInt64

		err := rows.Scan(
			&analytics.ID,
			&analytics.ScheduleID,
			&itemID,
			&analytics.ContentName,
			&analytics.ContentPath,
			&analytics.RTPOutput,
			&analytics.PlayedAt,
			&analytics.Duration,
			&analytics.PlayType,
		)
		if err != nil {
			logger.Error("Failed to scan content analytics: %v", err)
			continue
		}

		if itemID.Valid {
			analytics.ItemID = itemID.Int64
		}

		items = append(items, analytics)
	}

	// Calculate total pages
	totalPages := totalItems / pagination.Limit
	if totalItems%pagination.Limit > 0 {
		totalPages++
	}

	return models.ContentAnalyticsListResult{
		Items:      items,
		TotalItems: totalItems,
		TotalPages: totalPages,
		Page:       pagination.Page,
		Limit:      pagination.Limit,
	}, nil
}

// GetContentSummary retrieves a summary of analytics for a specific piece of content
func (r *AnalyticsRepository) GetContentSummary(scheduleID int64, contentPath string) (models.ContentAnalyticsSummary, error) {
	var summary models.ContentAnalyticsSummary
	var err error

	// Get content name and last played time
	var contentName, lastPlayedAt string
	if scheduleID > 0 {
		err = r.db.QueryRow(`
			SELECT content_name, played_at
			FROM content_analytics
			WHERE schedule_id = $1 AND content_path = $2
			ORDER BY played_at DESC
			LIMIT 1
		`, scheduleID, contentPath).Scan(&contentName, &lastPlayedAt)
	} else {
		err = r.db.QueryRow(`
			SELECT content_name, played_at
			FROM content_analytics
			WHERE content_path = $1
			ORDER BY played_at DESC
			LIMIT 1
		`, contentPath).Scan(&contentName, &lastPlayedAt)
	}

	if err != nil {
		logger.Error("Failed to get content summary: %v", err)
		return summary, err
	}

	summary.ContentName = contentName
	summary.ContentPath = contentPath
	summary.LastPlayedAt = lastPlayedAt

	// Get total plays and duration
	if scheduleID > 0 {
		err = r.db.QueryRow(`
			SELECT COUNT(*), SUM(duration)
			FROM content_analytics
			WHERE schedule_id = $1 AND content_path = $2
		`, scheduleID, contentPath).Scan(&summary.TotalPlays, &summary.TotalDuration)
	} else {
		err = r.db.QueryRow(`
			SELECT COUNT(*), SUM(duration)
			FROM content_analytics
			WHERE content_path = $1
		`, contentPath).Scan(&summary.TotalPlays, &summary.TotalDuration)
	}

	if err != nil {
		logger.Error("Failed to get content total plays and duration: %v", err)
		return summary, err
	}

	// Get unique output channels
	var rows *sql.Rows
	if scheduleID > 0 {
		rows, err = r.db.Query(`
			SELECT DISTINCT rtp_output
			FROM content_analytics
			WHERE schedule_id = $1 AND content_path = $2
		`, scheduleID, contentPath)
	} else {
		rows, err = r.db.Query(`
			SELECT DISTINCT rtp_output
			FROM content_analytics
			WHERE content_path = $1
		`, contentPath)
	}

	if err != nil {
		logger.Error("Failed to get content output channels: %v", err)
		return summary, err
	}
	defer rows.Close()

	for rows.Next() {
		var output string
		err := rows.Scan(&output)
		if err != nil {
			logger.Error("Failed to scan output channel: %v", err)
			continue
		}
		summary.OutputChannels = append(summary.OutputChannels, output)
	}

	return summary, nil
}

// GetScheduleSummary retrieves a summary of analytics for a specific schedule
func (r *AnalyticsRepository) GetScheduleSummary(scheduleID int64) (models.ScheduleAnalyticsSummary, error) {
	var summary models.ScheduleAnalyticsSummary
	summary.ScheduleID = scheduleID

	// Create a context with timeout to prevent long-running queries
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Get schedule name
	scheduleRepo := NewScheduleRepository(r.db)
	schedule, err := scheduleRepo.FindById(int(scheduleID))
	if err != nil {
		logger.Error("Failed to get schedule: %v", err)
		return summary, err
	}
	summary.ScheduleName = schedule.Name

	// Use a transaction to ensure consistent reads and reduce lock contention
	tx, err := r.db.BeginTx(ctx, &sql.TxOptions{ReadOnly: true})
	if err != nil {
		logger.Error("Failed to begin transaction: %v", err)
		return summary, err
	}
	defer tx.Rollback() // Will be ignored if transaction is committed

	// Get total content played and play time
	err = tx.QueryRowContext(ctx, `
		SELECT COUNT(*), COALESCE(SUM(duration), 0)
		FROM content_analytics
		WHERE schedule_id = $1
	`, scheduleID).Scan(&summary.TotalContentPlayed, &summary.TotalPlayTime)

	if err != nil {
		logger.Error("Failed to get schedule total plays and duration: %v", err)
		return summary, err
	}

	// Get top content (most played) - using a single query with a subquery for efficiency
	rows, err := tx.QueryContext(ctx, `
		WITH top_content AS (
			SELECT
				content_path,
				content_name,
				COUNT(*) as play_count,
				SUM(duration) as total_duration,
				MAX(played_at) as last_played
			FROM content_analytics
			WHERE schedule_id = $1
			GROUP BY content_path, content_name
			ORDER BY play_count DESC
			LIMIT 10
		)
		SELECT
			tc.content_path,
			tc.content_name,
			tc.play_count,
			tc.total_duration,
			tc.last_played,
			STRING_AGG(DISTINCT ca.rtp_output, ',') as outputs
		FROM top_content tc
		JOIN content_analytics ca ON ca.content_path = tc.content_path AND ca.schedule_id = $2
		GROUP BY tc.content_path, tc.content_name, tc.play_count, tc.total_duration, tc.last_played
		ORDER BY tc.play_count DESC
	`, scheduleID, scheduleID)

	if err != nil {
		logger.Error("Failed to get top content: %v", err)
		return summary, err
	}
	defer rows.Close()

	for rows.Next() {
		var contentSummary models.ContentAnalyticsSummary
		var playCount int
		var totalDuration float64
		var lastPlayed string
		var outputsStr string

		err := rows.Scan(
			&contentSummary.ContentPath,
			&contentSummary.ContentName,
			&playCount,
			&totalDuration,
			&lastPlayed,
			&outputsStr,
		)
		if err != nil {
			logger.Error("Failed to scan top content: %v", err)
			continue
		}

		contentSummary.TotalPlays = playCount
		contentSummary.TotalDuration = totalDuration
		contentSummary.LastPlayedAt = lastPlayed

		// Parse the concatenated outputs
		if outputsStr != "" {
			contentSummary.OutputChannels = strings.Split(outputsStr, ",")
		}

		summary.TopContent = append(summary.TopContent, contentSummary)
	}

	// Get all RTP outputs for this schedule in a single query
	var outputsStr string
	err = tx.QueryRowContext(ctx, `
		SELECT STRING_AGG(DISTINCT rtp_output, ',')
		FROM content_analytics
		WHERE schedule_id = $1
	`, scheduleID).Scan(&outputsStr)

	if err != nil && err != sql.ErrNoRows {
		logger.Error("Failed to get RTP outputs: %v", err)
		return summary, err
	}

	if outputsStr != "" {
		summary.RTPOutputs = strings.Split(outputsStr, ",")
	}

	// Commit the transaction
	if err = tx.Commit(); err != nil {
		logger.Error("Failed to commit transaction: %v", err)
		return summary, err
	}

	return summary, nil
}

// GetAnalyticsForDateRange retrieves analytics data for a specific date range with pagination
func (r *AnalyticsRepository) GetAnalyticsForDateRange(scheduleID int64, startDate, endDate string, pagination *models.Pagination) (models.ContentAnalyticsListResult, error) {
	var results []models.ContentAnalytics

	// Parse the date strings
	start, err := time.Parse(time.RFC3339, startDate)
	if err != nil {
		logger.Error("Failed to parse start date: %v", err)
		return models.ContentAnalyticsListResult{}, err
	}

	end, err := time.Parse(time.RFC3339, endDate)
	if err != nil {
		logger.Error("Failed to parse end date: %v", err)
		return models.ContentAnalyticsListResult{}, err
	}

	// Format dates for SQLite query
	startStr := start.Format(time.RFC3339)
	endStr := end.Format(time.RFC3339)

	// Create a context with timeout to prevent long-running queries
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// First, get the total count for pagination
	var totalItems int
	var countQuery string
	var countArgs []interface{}

	if scheduleID > 0 {
		countQuery = `
			SELECT COUNT(*)
			FROM content_analytics
			WHERE schedule_id = $1 AND played_at BETWEEN $2 AND $3
		`
		countArgs = []interface{}{scheduleID, startStr, endStr}
	} else {
		countQuery = `
			SELECT COUNT(*)
			FROM content_analytics
			WHERE played_at BETWEEN $1 AND $2
		`
		countArgs = []interface{}{startStr, endStr}
	}

	err = r.db.QueryRowContext(ctx, countQuery, countArgs...).Scan(&totalItems)
	if err != nil {
		logger.Error("Failed to get total count for date range analytics: %v", err)
		return models.ContentAnalyticsListResult{}, err
	}

	// If pagination is provided, use it; otherwise, use default values
	var page, limit int
	if pagination != nil {
		page = pagination.Page
		limit = pagination.Limit
	} else {
		// Default pagination for backward compatibility
		page = 1
		limit = 1000
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Build the query with parameters (now with proper pagination)
	var query string
	var args []interface{}

	if scheduleID > 0 {
		query = `
			SELECT id, schedule_id, item_id, content_name, content_path, rtp_output, played_at, duration, play_type
			FROM content_analytics
			WHERE schedule_id = $1 AND played_at BETWEEN $2 AND $3
			ORDER BY played_at DESC
			LIMIT $4 OFFSET $5
		`
		args = []interface{}{scheduleID, startStr, endStr, limit, offset}
	} else {
		query = `
			SELECT id, schedule_id, item_id, content_name, content_path, rtp_output, played_at, duration, play_type
			FROM content_analytics
			WHERE played_at BETWEEN $1 AND $2
			ORDER BY played_at DESC
			LIMIT $3 OFFSET $4
		`
		args = []interface{}{startStr, endStr, limit, offset}
	}

	// Execute the query with context
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		if err == context.DeadlineExceeded {
			logger.Error("Query timeout for analytics date range")
			return models.ContentAnalyticsListResult{}, errors.New("query timeout - please try with a smaller date range")
		}
		logger.Error("Failed to get analytics for date range: %v", err)
		return models.ContentAnalyticsListResult{}, err
	}
	defer rows.Close()

	// Process results
	for rows.Next() {
		var analytics models.ContentAnalytics
		var itemID sql.NullInt64

		err := rows.Scan(
			&analytics.ID,
			&analytics.ScheduleID,
			&itemID,
			&analytics.ContentName,
			&analytics.ContentPath,
			&analytics.RTPOutput,
			&analytics.PlayedAt,
			&analytics.Duration,
			&analytics.PlayType,
		)
		if err != nil {
			logger.Error("Failed to scan analytics: %v", err)
			continue
		}

		if itemID.Valid {
			analytics.ItemID = itemID.Int64
		}

		results = append(results, analytics)
	}

	// Check for errors after iteration
	if err = rows.Err(); err != nil {
		logger.Error("Error during row iteration: %v", err)
		return models.ContentAnalyticsListResult{}, err
	}

	// Calculate total pages
	totalPages := totalItems / limit
	if totalItems%limit > 0 {
		totalPages++
	}

	return models.ContentAnalyticsListResult{
		Items:      results,
		TotalItems: totalItems,
		TotalPages: totalPages,
		Page:       page,
		Limit:      limit,
	}, nil
}

// GetAnalyticsForDateRangeSimple is for backward compatibility - returns just the items array
func (r *AnalyticsRepository) GetAnalyticsForDateRangeSimple(scheduleID int64, startDate, endDate string) ([]models.ContentAnalytics, error) {
	result, err := r.GetAnalyticsForDateRange(scheduleID, startDate, endDate, nil)
	if err != nil {
		return nil, err
	}
	return result.Items, nil
}

// DeleteAnalyticsForSchedule deletes all analytics records for a specific schedule
func (r *AnalyticsRepository) DeleteAnalyticsForSchedule(scheduleID int64) error {
	result, err := r.db.Exec(`
		DELETE FROM content_analytics
		WHERE schedule_id = $1
	`, scheduleID)

	if err != nil {
		logger.Error("Failed to delete analytics for schedule %d: %v", scheduleID, err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("Failed to get rows affected for analytics deletion: %v", err)
		return err
	}

	logger.Log("Deleted %d analytics records for schedule %d", rowsAffected, scheduleID)
	return nil
}

// GetAnalyticsStatsForDateRange retrieves aggregate statistics for a date range (without pagination)
func (r *AnalyticsRepository) GetAnalyticsStatsForDateRange(scheduleID int64, startDate, endDate string) (models.AnalyticsStats, error) {
	var stats models.AnalyticsStats

	// Parse the date strings
	start, err := time.Parse(time.RFC3339, startDate)
	if err != nil {
		logger.Error("Failed to parse start date: %v", err)
		return stats, err
	}

	end, err := time.Parse(time.RFC3339, endDate)
	if err != nil {
		logger.Error("Failed to parse end date: %v", err)
		return stats, err
	}

	// Format dates for SQLite query
	startStr := start.Format(time.RFC3339)
	endStr := end.Format(time.RFC3339)

	// Create a context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get total count, total duration
	var totalCount int
	var totalDuration float64
	var countQuery string
	var countArgs []interface{}

	if scheduleID > 0 {
		countQuery = `
			SELECT COUNT(*), COALESCE(SUM(duration), 0)
			FROM content_analytics
			WHERE schedule_id = $1 AND played_at BETWEEN $2 AND $3
		`
		countArgs = []interface{}{scheduleID, startStr, endStr}
	} else {
		countQuery = `
			SELECT COUNT(*), COALESCE(SUM(duration), 0)
			FROM content_analytics
			WHERE played_at BETWEEN $1 AND $2
		`
		countArgs = []interface{}{startStr, endStr}
	}

	err = r.db.QueryRowContext(ctx, countQuery, countArgs...).Scan(&totalCount, &totalDuration)
	if err != nil {
		logger.Error("Failed to get aggregate stats for date range: %v", err)
		return stats, err
	}

	stats.TotalPlays = totalCount
	stats.TotalDuration = totalDuration

	// Get unique output channels
	var outputQuery string
	var outputArgs []interface{}

	if scheduleID > 0 {
		outputQuery = `
			SELECT DISTINCT rtp_output
			FROM content_analytics
			WHERE schedule_id = $1 AND played_at BETWEEN $2 AND $3
		`
		outputArgs = []interface{}{scheduleID, startStr, endStr}
	} else {
		outputQuery = `
			SELECT DISTINCT rtp_output
			FROM content_analytics
			WHERE played_at BETWEEN $1 AND $2
		`
		outputArgs = []interface{}{startStr, endStr}
	}

	rows, err := r.db.QueryContext(ctx, outputQuery, outputArgs...)
	if err != nil {
		logger.Error("Failed to get unique outputs for date range: %v", err)
		return stats, err
	}
	defer rows.Close()

	var outputs []string
	for rows.Next() {
		var output string
		err := rows.Scan(&output)
		if err != nil {
			logger.Error("Failed to scan output: %v", err)
			continue
		}
		outputs = append(outputs, output)
	}

	stats.UniqueOutputs = len(outputs)

	return stats, nil
}
