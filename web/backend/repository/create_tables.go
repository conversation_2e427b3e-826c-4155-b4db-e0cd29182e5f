package repository

import (
	"database/sql"
	"showfer-web/service/logger"

	"golang.org/x/crypto/bcrypt"
)

// CreateScheduleTable creates the schedule table
func CreateScheduleTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS schedules (
			id SERIAL PRIMARY KEY,
			name TEXT NOT NULL,
			icon TEXT DEFAULT NULL,
			timezone TEXT NOT NULL,
			short_id TEXT NOT NULL,
			autosave BOOLEAN NOT NULL DEFAULT FALSE,
			output_url TEXT DEFAULT NULL,
			network_interface TEXT DEFAULT NULL,
			ads TEXT DEFAULT NULL,
			channels TEXT DEFAULT NULL,
			regular_days TEXT DEFAULT NULL,
			special_days TEXT DEFAULT NULL,
			fillers TEXT DEFAULT NULL,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		)
	`)
	if err != nil {
		logger.Error("Failed to create schedule table: %v", err)
		return err
	}
	return nil
}

// CreateConvertItemTable creates the convert_items table
func CreateConvertItemTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS convert_items (
			id SERIAL PRIMARY KEY,
			filename TEXT NOT NULL,
			name TEXT,
			location TEXT NOT NULL,
			duration REAL DEFAULT 0,
			status INTEGER DEFAULT 0,
			size BIGINT NOT NULL,
			storage_type INTEGER DEFAULT 0,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW(),
			c_location TEXT,
			description TEXT,
			episode TEXT,
			width INTEGER,
			height INTEGER,
			fps REAL,
			video_codec TEXT,
			audio_codec TEXT,
			bitrate INTEGER,
		    recorder_id INTEGER,
			codec_settings_version INTEGER,
			FOREIGN KEY (recorder_id) REFERENCES recorders(id) ON DELETE SET NULL
		)
	`)
	if err != nil {
		logger.Error("Failed to create convert_items table: %v", err)
		return err
	}

	// Create indexes
	_, err = db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_convert_items_location ON convert_items(location);
		CREATE INDEX IF NOT EXISTS idx_convert_items_filename_location ON convert_items(filename, location);
	`)
	if err != nil {
		logger.Error("Failed to create indexes: %v", err)
		return err
	}

	return nil
}

// CreateGuideTable creates the guides table
func CreateGuideTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS guides (
			id SERIAL PRIMARY KEY,
			schedule_id INTEGER NOT NULL,
			elements TEXT NOT NULL,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW(),
			FOREIGN KEY (schedule_id) REFERENCES schedules (id) ON DELETE CASCADE
		)
	`)
	if err != nil {
		logger.Error("Failed to create guides table: %v", err)
		return err
	}
	return nil
}

// CreateHistoryTable creates the history table
func CreateHistoryTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS history (
			id SERIAL PRIMARY KEY,
			schedule_id INTEGER NOT NULL,
			folder TEXT NOT NULL,
			file_id INTEGER NOT NULL,
			episode INTEGER DEFAULT NULL,
			created_at TIMESTAMP DEFAULT NOW()
		)
	`)
	if err != nil {
		logger.Error("Failed to create history table: %v", err)
		return err
	}
	return nil
}

// CreateRtpUrlTable creates the rtp_urls table
func CreateRtpUrlTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS rtp_urls (
			id SERIAL PRIMARY KEY,
			url TEXT NOT NULL,
			recorder_id INTEGER NOT NULL,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		)
	`)
	if err != nil {
		logger.Error("Failed to create rtp_urls table: %v", err)
		return err
	}
	return nil
}

// CreateRecorderTable creates the recorders table
func CreateRecorderTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS recorders (
			id SERIAL PRIMARY KEY,
			name TEXT NOT NULL,
			input TEXT NOT NULL,
			rtp_url_id INTEGER,
			duration TEXT NOT NULL,
			status TEXT NOT NULL,
			vcodec TEXT NOT NULL,
			acodec TEXT NOT NULL,
			resolution TEXT NOT NULL,
			fps REAL NOT NULL,
			sample_rate INTEGER NOT NULL,
			vbitrate INTEGER NOT NULL,
			abitrate INTEGER NOT NULL,
			max_vbitrate INTEGER NOT NULL,
			network_interface TEXT DEFAULT '',
			source_ip TEXT DEFAULT '',
			service_id INTEGER DEFAULT NULL,
			is_scheduled BOOLEAN DEFAULT FALSE,
			scheduled_start_time TIMESTAMP DEFAULT NULL,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW(),
			FOREIGN KEY (rtp_url_id) REFERENCES rtp_urls (id)
		)
	`)
	if err != nil {
		logger.Error("Failed to create recorders table: %v", err)
		return err
	}
	return nil
}

// CreateUsersTable creates the users table
func CreateUsersTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id SERIAL PRIMARY KEY,
			username TEXT NOT NULL UNIQUE,
			email TEXT NOT NULL UNIQUE,
			password TEXT NOT NULL,
			role TEXT NOT NULL,
			status TEXT NOT NULL DEFAULT 'approved',
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		)
	`)
	if err != nil {
		logger.Error("Failed to create users table: %v", err)
		return err
	}

	// Check if admin user exists
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM users WHERE username = 'admin'").Scan(&count)
	if err != nil {
		logger.Error("Failed to check for admin user: %v", err)
		return err
	}

	// Create default admin user if none exists
	if count == 0 {
		// Hash the default password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
		if err != nil {
			logger.Error("Failed to hash password: %v", err)
			return err
		}

		_, err = db.Exec(`
			INSERT INTO users (username, email, password, role)
			VALUES ($1, $2, $3, $4)
		`, "admin", "<EMAIL>", string(hashedPassword), "admin")

		if err != nil {
			logger.Error("Failed to create admin user: %v", err)
			return err
		}

		logger.Log("Created default admin user")
	}

	return nil
}

// CreateContentAnalyticsTable creates the content_analytics table
func CreateContentAnalyticsTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS content_analytics (
			id SERIAL PRIMARY KEY,
			schedule_id INTEGER NOT NULL,
			item_id INTEGER,
			content_name TEXT NOT NULL,
			content_path TEXT NOT NULL,
			rtp_output TEXT NOT NULL,
			played_at TEXT NOT NULL,
			duration REAL DEFAULT 0,
			play_type TEXT NOT NULL,
			FOREIGN KEY (schedule_id) REFERENCES schedules(id)
		)
	`)
	if err != nil {
		logger.Error("Failed to create content_analytics table: %v", err)
		return err
	}

	// Create indexes
	_, err = db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_content_analytics_schedule_id ON content_analytics(schedule_id);
		CREATE INDEX IF NOT EXISTS idx_content_analytics_content_path ON content_analytics(content_path);
		CREATE INDEX IF NOT EXISTS idx_content_analytics_played_at ON content_analytics(played_at);
		CREATE INDEX IF NOT EXISTS idx_content_analytics_schedule_played_at ON content_analytics(schedule_id, played_at);
	`)
	if err != nil {
		logger.Error("Failed to create content_analytics indexes: %v", err)
		return err
	}

	return nil
}

// CreateLogsTable creates the logs table
func CreateLogsTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS logs (
			id SERIAL PRIMARY KEY,
			timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
			level TEXT NOT NULL,
			message TEXT NOT NULL,
			source TEXT NOT NULL,
			user_id INTEGER,
			request_id TEXT,
			metadata JSONB,
			created_at TIMESTAMP DEFAULT NOW(),
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
		)
	`)
	if err != nil {
		logger.Error("Failed to create logs table: %v", err)
		return err
	}

	// Create indexes for efficient querying
	_, err = db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp);
		CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level);
		CREATE INDEX IF NOT EXISTS idx_logs_source ON logs(source);
		CREATE INDEX IF NOT EXISTS idx_logs_user_id ON logs(user_id);
		CREATE INDEX IF NOT EXISTS idx_logs_request_id ON logs(request_id);
		CREATE INDEX IF NOT EXISTS idx_logs_level_timestamp ON logs(level, timestamp);
		CREATE INDEX IF NOT EXISTS idx_logs_source_timestamp ON logs(source, timestamp);
		CREATE INDEX IF NOT EXISTS idx_logs_message_gin ON logs USING gin(to_tsvector('english', message));
	`)
	if err != nil {
		logger.Error("Failed to create logs indexes: %v", err)
		return err
	}

	return nil
}
