package repository

import (
	"database/sql"
	"encoding/json"
	"showfer-web/models"
	"time"
)

type GuideRepository struct {
	db *sql.DB
}

func NewGuideRepository(db *sql.DB) *GuideRepository {
	return &GuideRepository{db: db}
}

func (r *GuideRepository) CreateGuide(guide models.Guide) (int64, error) {
	var id int64
	err := r.db.QueryRow(`
		INSERT INTO guides
			(created_at, updated_at, elements, schedule_id)
		VALUES ($1, $2, $3, $4)
		RETURNING id
	`,
		time.Now().Format(time.RFC3339),
		time.Now().Format(time.RFC3339),
		r.<PERSON><PERSON><PERSON><PERSON><PERSON>(guide.Elements),
		guide.ScheduleId,
	).Scan(&id)

	if err != nil {
		return 0, err
	}

	return id, nil
}

func (r *GuideRepository) UpdateGuide(guide models.Guide) error {
	_, err := r.db.Exec(`
		UPDATE guides SET updated_at = $1, elements = $2, schedule_id = $3 WHERE id = $4
	`,
		time.Now().Format(time.RFC3339),
		r.<PERSON><PERSON><PERSON><PERSON><PERSON>(guide.Elements),
		guide.ScheduleId,
		guide.ID,
	)

	return err
}

func (r *GuideRepository) FindGuideByScheduleID(scheduleId int64) (models.Guide, error) {
	row := r.db.QueryRow(`
		SELECT id, created_at, updated_at, schedule_id, elements
		FROM guides
		WHERE schedule_id = $1
	`, scheduleId)

	var d models.Guide
	var elements string

	err := row.Scan(&d.ID, &d.CreatedAt, &d.UpdatedAt, &d.ScheduleId, &elements)
	if err != nil {
		return models.Guide{}, err
	}
	err = json.Unmarshal([]byte(elements), &d.Elements)
	if err != nil {
		return models.Guide{}, err
	}

	return d, nil
}

func (r *GuideRepository) DeleteGuidee(scheduleId int) error {
	_, err := r.db.Exec(`DELETE FROM guides WHERE schedule_id = $1`, scheduleId)

	return err
}

func (r *GuideRepository) objectToJson(object any) string {
	jsonData, err := json.Marshal(object)
	if err != nil {
		panic(err)
	}

	return string(jsonData)
}
