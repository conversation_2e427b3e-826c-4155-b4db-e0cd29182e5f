package repository

import (
	"database/sql"
	"fmt"
	"showfer-web/models"
	"strings"
	"time"
)

// LogsRepository handles database operations for logs
type LogsRepository struct {
	db *sql.DB
}

// NewLogsRepository creates a new LogsRepository
func NewLogsRepository(db *sql.DB) *LogsRepository {
	return &LogsRepository{db: db}
}

// CreateLogEntry creates a new log entry in the database
func (r *LogsRepository) CreateLogEntry(entry *models.LogEntry) error {
	query := `
		INSERT INTO logs (timestamp, level, message, source, user_id, request_id, metadata)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id, created_at
	`
	
	var metadataJSON *string
	if entry.Metadata != nil {
		metadataJSON = entry.Metadata
	}

	err := r.db.QueryRow(
		query,
		entry.Timestamp,
		entry.Level,
		entry.Message,
		entry.Source,
		entry.UserID,
		entry.RequestID,
		metadataJSON,
	).Scan(&entry.ID, &entry.CreatedAt)

	return err
}

// GetLogs retrieves logs with filtering and pagination
func (r *LogsRepository) GetLogs(filter models.LogFilter) ([]models.LogEntry, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	// Build WHERE conditions
	if filter.StartDate != nil {
		conditions = append(conditions, fmt.Sprintf("timestamp >= $%d", argIndex))
		args = append(args, *filter.StartDate)
		argIndex++
	}

	if filter.EndDate != nil {
		conditions = append(conditions, fmt.Sprintf("timestamp <= $%d", argIndex))
		args = append(args, *filter.EndDate)
		argIndex++
	}

	if filter.Level != nil {
		conditions = append(conditions, fmt.Sprintf("level = $%d", argIndex))
		args = append(args, *filter.Level)
		argIndex++
	}

	if filter.Source != nil {
		conditions = append(conditions, fmt.Sprintf("source = $%d", argIndex))
		args = append(args, *filter.Source)
		argIndex++
	}

	if filter.UserID != nil {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, *filter.UserID)
		argIndex++
	}

	if filter.Search != nil && *filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("message ILIKE $%d", argIndex))
		args = append(args, "%"+*filter.Search+"%")
		argIndex++
	}

	// Build query
	query := "SELECT id, timestamp, level, message, source, user_id, request_id, metadata, created_at FROM logs"
	
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	query += " ORDER BY timestamp DESC"

	// Add pagination
	if filter.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, filter.Limit)
		argIndex++
	}

	if filter.Offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, filter.Offset)
	}

	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []models.LogEntry
	for rows.Next() {
		var log models.LogEntry
		var metadataJSON sql.NullString

		err := rows.Scan(
			&log.ID,
			&log.Timestamp,
			&log.Level,
			&log.Message,
			&log.Source,
			&log.UserID,
			&log.RequestID,
			&metadataJSON,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}

		if metadataJSON.Valid {
			log.Metadata = &metadataJSON.String
		}

		logs = append(logs, log)
	}

	return logs, rows.Err()
}

// GetLogStats returns statistics about logs
func (r *LogsRepository) GetLogStats(filter models.LogFilter) (*models.LogStats, error) {
	stats := &models.LogStats{
		LogsByLevel:  make(map[models.LogLevel]int),
		LogsBySource: make(map[string]int),
	}

	// Build WHERE conditions for filtering
	var conditions []string
	var args []interface{}
	argIndex := 1

	if filter.StartDate != nil {
		conditions = append(conditions, fmt.Sprintf("timestamp >= $%d", argIndex))
		args = append(args, *filter.StartDate)
		argIndex++
	}

	if filter.EndDate != nil {
		conditions = append(conditions, fmt.Sprintf("timestamp <= $%d", argIndex))
		args = append(args, *filter.EndDate)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	// Get total count
	countQuery := "SELECT COUNT(*) FROM logs" + whereClause
	err := r.db.QueryRow(countQuery, args...).Scan(&stats.TotalLogs)
	if err != nil {
		return nil, err
	}

	// Get logs by level
	levelQuery := "SELECT level, COUNT(*) FROM logs" + whereClause + " GROUP BY level"
	rows, err := r.db.Query(levelQuery, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var level models.LogLevel
		var count int
		if err := rows.Scan(&level, &count); err != nil {
			return nil, err
		}
		stats.LogsByLevel[level] = count
	}

	// Get logs by source
	sourceQuery := "SELECT source, COUNT(*) FROM logs" + whereClause + " GROUP BY source ORDER BY COUNT(*) DESC LIMIT 10"
	rows, err = r.db.Query(sourceQuery, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var source string
		var count int
		if err := rows.Scan(&source, &count); err != nil {
			return nil, err
		}
		stats.LogsBySource[source] = count
	}

	// Get last log time
	lastLogQuery := "SELECT MAX(timestamp) FROM logs" + whereClause
	var lastLogTime sql.NullTime
	err = r.db.QueryRow(lastLogQuery, args...).Scan(&lastLogTime)
	if err != nil {
		return nil, err
	}
	if lastLogTime.Valid {
		stats.LastLogTime = &lastLogTime.Time
	}

	return stats, nil
}

// GetLogSources returns all unique log sources
func (r *LogsRepository) GetLogSources() ([]string, error) {
	query := "SELECT DISTINCT source FROM logs ORDER BY source"
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var sources []string
	for rows.Next() {
		var source string
		if err := rows.Scan(&source); err != nil {
			return nil, err
		}
		sources = append(sources, source)
	}

	return sources, rows.Err()
}

// DeleteOldLogs deletes logs older than the specified duration
func (r *LogsRepository) DeleteOldLogs(olderThan time.Duration) (int64, error) {
	cutoffTime := time.Now().Add(-olderThan)
	
	result, err := r.db.Exec("DELETE FROM logs WHERE timestamp < $1", cutoffTime)
	if err != nil {
		return 0, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

// CountLogs returns the total number of logs matching the filter
func (r *LogsRepository) CountLogs(filter models.LogFilter) (int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	// Build WHERE conditions (same as GetLogs)
	if filter.StartDate != nil {
		conditions = append(conditions, fmt.Sprintf("timestamp >= $%d", argIndex))
		args = append(args, *filter.StartDate)
		argIndex++
	}

	if filter.EndDate != nil {
		conditions = append(conditions, fmt.Sprintf("timestamp <= $%d", argIndex))
		args = append(args, *filter.EndDate)
		argIndex++
	}

	if filter.Level != nil {
		conditions = append(conditions, fmt.Sprintf("level = $%d", argIndex))
		args = append(args, *filter.Level)
		argIndex++
	}

	if filter.Source != nil {
		conditions = append(conditions, fmt.Sprintf("source = $%d", argIndex))
		args = append(args, *filter.Source)
		argIndex++
	}

	if filter.UserID != nil {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, *filter.UserID)
		argIndex++
	}

	if filter.Search != nil && *filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("message ILIKE $%d", argIndex))
		args = append(args, "%"+*filter.Search+"%")
		argIndex++
	}

	query := "SELECT COUNT(*) FROM logs"
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := r.db.QueryRow(query, args...).Scan(&count)
	return count, err
}
