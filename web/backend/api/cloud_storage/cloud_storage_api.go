package cloud_storage

import (
	"database/sql"
	"net/http"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/storage"

	"github.com/gin-gonic/gin"
)

// CloudStorageAPI handles cloud storage related API endpoints
type CloudStorageAPI struct {
	db                      *sql.DB
	cloudStorageRepo        *repository.CloudStorageRepository
}

// NewCloudStorageAPI creates a new CloudStorageAPI
func NewCloudStorageAPI(db *sql.DB) *CloudStorageAPI {
	return &CloudStorageAPI{
		db:                      db,
		cloudStorageRepo:        repository.NewCloudStorageRepository(db),
	}
}

// GetCloudStorageSettings handles GET /api/v1/cloud-storage/settings
func (api *CloudStorageAPI) GetCloudStorageSettings(c *gin.Context) {
	settings, err := api.cloudStorageRepo.GetCloudStorageSettings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get cloud storage settings", "details": err.Error()})
		return
	}

	// Don't expose sensitive information like app key in the response
	settings.BackblazeAppKey = ""

	c.JSON(http.StatusOK, settings)
}

// UpdateCloudStorageSettings handles PUT /api/v1/cloud-storage/settings
func (api *CloudStorageAPI) UpdateCloudStorageSettings(c *gin.Context) {
	var input models.CloudStorageSettingsInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate storage type
	if input.StorageType < 0 || input.StorageType > 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid storage type. Must be 0 (Local), 1 (S3), or 2 (Backblaze)"})
		return
	}

	// If Backblaze is selected, validate required fields
	if input.StorageType == int(models.StorageTypeBackblaze) && input.IsEnabled {
		if input.BackblazeKeyID == "" || input.BackblazeAppKey == "" || input.BackblazeBucket == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Backblaze Key ID, App Key, and Bucket are required for Backblaze storage"})
			return
		}
	}

	err := api.cloudStorageRepo.UpdateCloudStorageSettings(input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update cloud storage settings", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Cloud storage settings updated successfully"})
}

// TestCloudStorageConnection handles POST /api/v1/cloud-storage/test-connection
func (api *CloudStorageAPI) TestCloudStorageConnection(c *gin.Context) {
	var input models.CloudStorageSettingsInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Only test Backblaze connections for now
	if input.StorageType != int(models.StorageTypeBackblaze) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Connection testing is only supported for Backblaze storage"})
		return
	}

	// Validate required fields
	if input.BackblazeKeyID == "" || input.BackblazeAppKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Backblaze Key ID and App Key are required"})
		return
	}

	// Create temporary settings for testing
	testSettings := models.CloudStorageSettings{
		StorageType:       input.StorageType,
		BackblazeKeyID:    input.BackblazeKeyID,
		BackblazeAppKey:   input.BackblazeAppKey,
		BackblazeBucket:   input.BackblazeBucket,
		BackblazeEndpoint: input.BackblazeEndpoint,
		IsEnabled:         true,
	}

	// Test connection
	backblazeService := storage.NewBackblazeService(testSettings)
	err := backblazeService.TestConnection()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Connection test failed", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Connection test successful"})
}

// ListBackblazeBuckets handles GET /api/v1/cloud-storage/backblaze/buckets
func (api *CloudStorageAPI) ListBackblazeBuckets(c *gin.Context) {
	// Get current settings
	settings, err := api.cloudStorageRepo.GetCloudStorageSettings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get cloud storage settings", "details": err.Error()})
		return
	}

	// Check if Backblaze is configured
	if settings.StorageType != int(models.StorageTypeBackblaze) || !settings.IsEnabled {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Backblaze storage is not configured or enabled"})
		return
	}

	if settings.BackblazeKeyID == "" || settings.BackblazeAppKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Backblaze credentials are not configured"})
		return
	}

	// List buckets
	backblazeService := storage.NewBackblazeService(settings)
	buckets, err := backblazeService.ListBuckets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list Backblaze buckets", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"buckets": buckets})
}

// ListBackblazeBucketsWithCredentials handles POST /api/v1/cloud-storage/backblaze/buckets-with-credentials
func (api *CloudStorageAPI) ListBackblazeBucketsWithCredentials(c *gin.Context) {
	var input struct {
		BackblazeKeyID  string `json:"backblaze_key_id" binding:"required"`
		BackblazeAppKey string `json:"backblaze_app_key" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Create temporary settings for listing buckets
	testSettings := models.CloudStorageSettings{
		StorageType:       int(models.StorageTypeBackblaze),
		BackblazeKeyID:    input.BackblazeKeyID,
		BackblazeAppKey:   input.BackblazeAppKey,
		BackblazeEndpoint: "https://s3.us-west-002.backblazeb2.com",
		IsEnabled:         true,
	}

	// List buckets
	backblazeService := storage.NewBackblazeService(testSettings)
	buckets, err := backblazeService.ListBuckets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list Backblaze buckets", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"buckets": buckets})
}
