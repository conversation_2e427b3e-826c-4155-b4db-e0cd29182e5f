package detector

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/url"
	"os/exec"
	"regexp"
	"showfer-web/models"
	"showfer-web/service/logger"
	"strconv"
	"strings"
	"time"
)

// DetectionMethod represents the method used for detection
type DetectionMethod string

const (
	FFmpegMethod     DetectionMethod = "ffmpeg"
	GStreamerMethod  DetectionMethod = "gstreamer"
)

// DetectionOptions contains options for stream detection
type DetectionOptions struct {
	Timeout   int             `json:"timeout"`   // Timeout in seconds
	Interface string          `json:"interface"` // Network interface to use
	Method    DetectionMethod `json:"method"`    // Detection method
}

// StreamInfo contains information about the detected stream
type StreamInfo struct {
	URL       string                `json:"url"`
	Services  []models.ServiceInfo  `json:"services"`
	VideoInfo []models.VideoInfo    `json:"video_info"`
	AudioInfo []models.AudioInfo    `json:"audio_info"`
	TsSync    bool                  `json:"ts_sync"`
	Error     string                `json:"error,omitempty"`
}

// FFprobeOutput represents the structure of ffprobe JSON output
type FFprobeOutput struct {
	Programs []struct {
		ProgramID int `json:"program_id"`
		ProgramNum int `json:"program_num"`
		PMTPid    int `json:"pmt_pid"`
		Streams   []struct {
			Index     int    `json:"index"`
			CodecName string `json:"codec_name"`
			CodecType string `json:"codec_type"`
			Width     int    `json:"width,omitempty"`
			Height    int    `json:"height,omitempty"`
			RFrameRate string `json:"r_frame_rate,omitempty"`
			Channels  int    `json:"channels,omitempty"`
			SampleRate string `json:"sample_rate,omitempty"`
		} `json:"streams"`
		Tags struct {
			ServiceName string `json:"service_name,omitempty"`
		} `json:"tags,omitempty"`
	} `json:"programs"`
	Streams []struct {
		Index     int    `json:"index"`
		CodecName string `json:"codec_name"`
		CodecType string `json:"codec_type"`
		Width     int    `json:"width,omitempty"`
		Height    int    `json:"height,omitempty"`
		RFrameRate string `json:"r_frame_rate,omitempty"`
		Channels  int    `json:"channels,omitempty"`
		SampleRate string `json:"sample_rate,omitempty"`
	} `json:"streams"`
}

// DefaultDetectionOptions returns default detection options
func DefaultDetectionOptions() DetectionOptions {
	return DetectionOptions{
		Timeout:   10,
		Interface: "",
		Method:    FFmpegMethod,
	}
}

// DetectServices detects services in an RTP stream
func DetectServices(rtpURL string, options DetectionOptions) (*StreamInfo, error) {
	logger.Log("Starting service detection for URL: %s", rtpURL)

	streamInfo := &StreamInfo{
		URL:       rtpURL,
		Services:  []models.ServiceInfo{},
		VideoInfo: []models.VideoInfo{},
		AudioInfo: []models.AudioInfo{},
		TsSync:    false,
	}

	// Parse RTP URL to extract components
	parsedURL, err := parseRtpURL(rtpURL)
	if err != nil {
		streamInfo.Error = fmt.Sprintf("Failed to parse RTP URL: %v", err)
		return streamInfo, err
	}

	// Override interface from options if specified
	if options.Interface != "" {
		parsedURL.Interface = options.Interface
	}

	// Build the final RTP URL for ffprobe
	finalURL := buildRtpURL(parsedURL)
	logger.Log("Using final RTP URL for detection: %s", finalURL)

	switch options.Method {
	case FFmpegMethod:
		return detectWithFFmpeg(finalURL, parsedURL, options, streamInfo)
	case GStreamerMethod:
		return detectWithGStreamer(finalURL, parsedURL, options, streamInfo)
	default:
		return detectWithFFmpeg(finalURL, parsedURL, options, streamInfo)
	}
}

// RtpURLInfo contains parsed RTP URL information
type RtpURLInfo struct {
	Address     string
	Port        int
	Path        string
	Interface   string
	LocalAddr   string
	IsMulticast bool
}

// parseRtpURL parses an RTP URL and extracts components
func parseRtpURL(rtpURL string) (*RtpURLInfo, error) {
	// Remove rtp:// prefix
	urlWithoutPrefix := strings.TrimPrefix(rtpURL, "rtp://")

	// Parse URL to handle query parameters
	parsedURL, err := url.Parse("dummy://" + urlWithoutPrefix)
	if err != nil {
		return nil, fmt.Errorf("invalid URL format: %v", err)
	}

	// Extract host and port
	host := parsedURL.Hostname()
	portStr := parsedURL.Port()
	if portStr == "" {
		return nil, fmt.Errorf("port is required in RTP URL")
	}

	port, err := strconv.Atoi(portStr)
	if err != nil {
		return nil, fmt.Errorf("invalid port number: %v", err)
	}

	// Validate IP address
	ip := net.ParseIP(host)
	if ip == nil {
		return nil, fmt.Errorf("invalid IP address: %s", host)
	}

	// Check if it's multicast
	isMulticast := ip.IsMulticast()

	// Extract query parameters
	query := parsedURL.Query()
	iface := query.Get("iface")
	localAddr := query.Get("localaddr")

	return &RtpURLInfo{
		Address:     host,
		Port:        port,
		Path:        parsedURL.Path,
		Interface:   iface,
		LocalAddr:   localAddr,
		IsMulticast: isMulticast,
	}, nil
}

// buildRtpURL builds the final RTP URL for ffprobe
func buildRtpURL(info *RtpURLInfo) string {
	baseURL := fmt.Sprintf("rtp://%s:%d%s", info.Address, info.Port, info.Path)

	var params []string
	if info.Interface != "" {
		params = append(params, fmt.Sprintf("iface=%s", info.Interface))
	}
	if info.LocalAddr != "" {
		params = append(params, fmt.Sprintf("localaddr=%s", info.LocalAddr))
	}

	if len(params) > 0 {
		baseURL += "?" + strings.Join(params, "&")
	}

	return baseURL
}

// detectWithFFmpeg uses FFmpeg/ffprobe to detect services
func detectWithFFmpeg(rtpURL string, urlInfo *RtpURLInfo, options DetectionOptions, streamInfo *StreamInfo) (*StreamInfo, error) {
	logger.Log("Using FFmpeg method for service detection")

	// Build ffprobe command
	args := []string{
		"-v", "quiet",
		"-print_format", "json",
		"-show_programs",
		"-show_streams",
		"-analyzeduration", "5000000", // 5 seconds
		"-probesize", "5000000",       // 5MB
	}

	// Add network interface options for multicast
	if urlInfo.IsMulticast && urlInfo.Interface != "" {
		// For multicast, we need to bind to the specific interface
		localIP, err := getInterfaceIP(urlInfo.Interface)
		if err != nil {
			logger.Error("Failed to get IP for interface %s: %v", urlInfo.Interface, err)
		} else {
			args = append(args, "-localaddr", localIP)
		}
	}

	// Add timeout
	args = append(args, "-timeout", fmt.Sprintf("%d000000", options.Timeout)) // Convert to microseconds

	// Add input URL
	args = append(args, "-i", rtpURL)

	logger.Log("Running ffprobe command: ffprobe %s", strings.Join(args, " "))

	// Execute ffprobe with timeout
	cmd := exec.Command("ffprobe", args...)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Set up timeout
	done := make(chan error, 1)
	go func() {
		done <- cmd.Run()
	}()

	select {
	case err := <-done:
		if err != nil {
			errorMsg := fmt.Sprintf("ffprobe failed: %v\nStderr: %s", err, stderr.String())
			logger.Error(errorMsg)
			streamInfo.Error = errorMsg
			return streamInfo, err
		}
	case <-time.After(time.Duration(options.Timeout+5) * time.Second):
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		errorMsg := "ffprobe timeout exceeded"
		logger.Error(errorMsg)
		streamInfo.Error = errorMsg
		return streamInfo, errors.New(errorMsg)
	}

	// Parse ffprobe output
	output := stdout.String()
	logger.Log("ffprobe output length: %d bytes", len(output))

	if len(output) == 0 {
		errorMsg := "ffprobe returned empty output"
		logger.Error(errorMsg)
		streamInfo.Error = errorMsg
		return streamInfo, errors.New(errorMsg)
	}

	return parseFFprobeOutput(output, streamInfo)
}

// parseFFprobeOutput parses the JSON output from ffprobe
func parseFFprobeOutput(output string, streamInfo *StreamInfo) (*StreamInfo, error) {
	var ffprobeData FFprobeOutput

	if err := json.Unmarshal([]byte(output), &ffprobeData); err != nil {
		logger.Error("Failed to parse ffprobe JSON output: %v", err)
		streamInfo.Error = fmt.Sprintf("Failed to parse ffprobe output: %v", err)
		return streamInfo, err
	}

	logger.Log("Parsed ffprobe data: %d programs, %d streams", len(ffprobeData.Programs), len(ffprobeData.Streams))

	// If we have programs, process them (MPEG-TS with multiple services)
	if len(ffprobeData.Programs) > 0 {
		return parsePrograms(ffprobeData, streamInfo)
	}

	// If no programs but we have streams, treat as single service
	if len(ffprobeData.Streams) > 0 {
		return parseSingleService(ffprobeData, streamInfo)
	}

	// No programs or streams found
	streamInfo.Error = "No programs or streams detected in RTP stream"
	return streamInfo, fmt.Errorf("no programs or streams detected")
}

// parsePrograms processes multiple programs/services from MPEG-TS
func parsePrograms(ffprobeData FFprobeOutput, streamInfo *StreamInfo) (*StreamInfo, error) {
	logger.Log("Processing %d programs from MPEG-TS stream", len(ffprobeData.Programs))

	// Create a map of global streams by index for easy lookup
	globalStreams := make(map[int]struct {
		CodecName string `json:"codec_name"`
		CodecType string `json:"codec_type"`
		Width     int    `json:"width,omitempty"`
		Height    int    `json:"height,omitempty"`
		Channels  int    `json:"channels,omitempty"`
	})

	for _, stream := range ffprobeData.Streams {
		globalStreams[stream.Index] = struct {
			CodecName string `json:"codec_name"`
			CodecType string `json:"codec_type"`
			Width     int    `json:"width,omitempty"`
			Height    int    `json:"height,omitempty"`
			Channels  int    `json:"channels,omitempty"`
		}{
			CodecName: stream.CodecName,
			CodecType: stream.CodecType,
			Width:     stream.Width,
			Height:    stream.Height,
			Channels:  stream.Channels,
		}
	}

	for _, program := range ffprobeData.Programs {
		serviceName := program.Tags.ServiceName
		if serviceName == "" {
			serviceName = fmt.Sprintf("Service %d", program.ProgramNum)
		}

		// Initialize service with basic info
		service := models.ServiceInfo{
			ServiceID:   program.ProgramNum,
			ServiceName: serviceName,
		}

		// Process streams for this program to get video/audio info
		var videoInfo models.VideoInfo
		var audioInfo models.AudioInfo
		var audioTracks []models.AudioInfo

		for _, stream := range program.Streams {
			// Get complete stream info from global streams
			globalStream, exists := globalStreams[stream.Index]
			if !exists {
				logger.Log("Warning: Global stream info not found for index %d", stream.Index)
				continue
			}

			switch globalStream.CodecType {
			case "video":
				videoCodec := normalizeCodecName(globalStream.CodecName)
				videoResolution := fmt.Sprintf("%dx%d", globalStream.Width, globalStream.Height)

				// Set per-service video info
				service.VideoCodec = videoCodec
				service.VideoResolution = videoResolution

				// Also maintain the old structure for backward compatibility
				videoInfo = models.VideoInfo{
					Resolution: videoResolution,
					Codec:      videoCodec,
				}
			case "audio":
				audioCodec := normalizeCodecName(globalStream.CodecName)
				audioChannels := globalStream.Channels

				// Add to audio tracks array for multiple audio support
				audioTrack := models.AudioInfo{
					Channels: audioChannels,
					Codec:    audioCodec,
				}
				audioTracks = append(audioTracks, audioTrack)

				// Set per-service audio info (legacy fields - use first audio track)
				if service.AudioCodec == "" {
					service.AudioCodec = audioCodec
					service.AudioChannels = audioChannels

					// Also maintain the old structure for backward compatibility
					audioInfo = models.AudioInfo{
						Channels: audioChannels,
						Codec:    audioCodec,
					}
				}
			}
		}

		// Set the audio tracks array in the service
		service.AudioTracks = audioTracks

		streamInfo.Services = append(streamInfo.Services, service)
		streamInfo.VideoInfo = append(streamInfo.VideoInfo, videoInfo)
		streamInfo.AudioInfo = append(streamInfo.AudioInfo, audioInfo)
	}

	if len(streamInfo.Services) > 0 {
		streamInfo.TsSync = true
		logger.Log("Successfully detected %d services", len(streamInfo.Services))
	}

	return streamInfo, nil
}

// parseSingleService processes a single service stream
func parseSingleService(ffprobeData FFprobeOutput, streamInfo *StreamInfo) (*StreamInfo, error) {
	logger.Log("Processing single service stream with %d streams", len(ffprobeData.Streams))

	// Initialize service with basic info
	service := models.ServiceInfo{
		ServiceID:   1,
		ServiceName: "Main Service",
	}

	var videoInfo models.VideoInfo
	var audioInfo models.AudioInfo
	var audioTracks []models.AudioInfo

	for _, stream := range ffprobeData.Streams {
		switch stream.CodecType {
		case "video":
			videoCodec := normalizeCodecName(stream.CodecName)
			videoResolution := fmt.Sprintf("%dx%d", stream.Width, stream.Height)

			// Set per-service video info
			service.VideoCodec = videoCodec
			service.VideoResolution = videoResolution

			// Also maintain the old structure for backward compatibility
			videoInfo = models.VideoInfo{
				Resolution: videoResolution,
				Codec:      videoCodec,
			}
		case "audio":
			audioCodec := normalizeCodecName(stream.CodecName)
			audioChannels := stream.Channels

			// Add to audio tracks array for multiple audio support
			audioTrack := models.AudioInfo{
				Channels: audioChannels,
				Codec:    audioCodec,
			}
			audioTracks = append(audioTracks, audioTrack)

			// Set per-service audio info (legacy fields - use first audio track)
			if service.AudioCodec == "" {
				service.AudioCodec = audioCodec
				service.AudioChannels = audioChannels

				// Also maintain the old structure for backward compatibility
				audioInfo = models.AudioInfo{
					Channels: audioChannels,
					Codec:    audioCodec,
				}
			}
		}
	}

	// Set the audio tracks array in the service
	service.AudioTracks = audioTracks

	streamInfo.Services = append(streamInfo.Services, service)
	streamInfo.VideoInfo = append(streamInfo.VideoInfo, videoInfo)
	streamInfo.AudioInfo = append(streamInfo.AudioInfo, audioInfo)
	streamInfo.TsSync = true

	logger.Log("Successfully detected single service")
	return streamInfo, nil
}

// normalizeCodecName converts codec names to user-friendly format
func normalizeCodecName(codecName string) string {
	switch strings.ToLower(codecName) {
	case "h264":
		return "H.264"
	case "h265", "hevc":
		return "H.265"
	case "mpeg2video":
		return "MPEG-2"
	case "aac":
		return "AAC"
	case "mp2":
		return "MP2"
	case "ac3":
		return "AC-3"
	case "eac3":
		return "E-AC-3"
	default:
		return strings.ToUpper(codecName)
	}
}

// getInterfaceIP gets the IP address of a network interface
func getInterfaceIP(interfaceName string) (string, error) {
	iface, err := net.InterfaceByName(interfaceName)
	if err != nil {
		return "", fmt.Errorf("failed to get interface %s: %v", interfaceName, err)
	}

	addrs, err := iface.Addrs()
	if err != nil {
		return "", fmt.Errorf("failed to get addresses for interface %s: %v", interfaceName, err)
	}

	for _, addr := range addrs {
		var ip net.IP
		switch v := addr.(type) {
		case *net.IPNet:
			ip = v.IP
		case *net.IPAddr:
			ip = v.IP
		}

		if ip.IsLoopback() {
			continue
		}

		if ip.To4() == nil {
			continue
		}

		return ip.String(), nil
	}

	return "", fmt.Errorf("no IPv4 address found for interface %s", interfaceName)
}

// detectWithGStreamer uses GStreamer to detect services (fallback method)
func detectWithGStreamer(rtpURL string, urlInfo *RtpURLInfo, options DetectionOptions, streamInfo *StreamInfo) (*StreamInfo, error) {
	logger.Log("Using GStreamer method for service detection")

	// For now, implement a basic GStreamer detection
	// This is a simplified implementation that can be expanded later
	args := []string{
		"-v",
		"--gst-debug-level=1",
	}

	// Add timeout
	args = append(args, "--timeout", fmt.Sprintf("%d", options.Timeout))

	// Add the RTP URL
	args = append(args, rtpURL)

	logger.Log("Running gst-discoverer-1.0 command: gst-discoverer-1.0 %s", strings.Join(args, " "))

	cmd := exec.Command("gst-discoverer-1.0", args...)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Set up timeout
	done := make(chan error, 1)
	go func() {
		done <- cmd.Run()
	}()

	select {
	case err := <-done:
		if err != nil {
			errorMsg := fmt.Sprintf("gst-discoverer failed: %v\nStderr: %s", err, stderr.String())
			logger.Error(errorMsg)
			streamInfo.Error = errorMsg
			return streamInfo, err
		}
	case <-time.After(time.Duration(options.Timeout+5) * time.Second):
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		errorMsg := "gst-discoverer timeout exceeded"
		logger.Error(errorMsg)
		streamInfo.Error = errorMsg
		return streamInfo, errors.New(errorMsg)
	}

	// Parse GStreamer output (simplified)
	output := stdout.String()
	return parseGStreamerOutput(output, streamInfo)
}

// parseGStreamerOutput parses GStreamer discoverer output
func parseGStreamerOutput(output string, streamInfo *StreamInfo) (*StreamInfo, error) {
	logger.Log("Parsing GStreamer output: %d bytes", len(output))

	// This is a simplified parser for GStreamer output
	// In a full implementation, you would parse the detailed GStreamer output

	// Look for video and audio information in the output
	lines := strings.Split(output, "\n")

	var hasVideo, hasAudio bool
	var videoCodec, audioCodec string
	var width, height, channels int

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// Look for video information
		if strings.Contains(line, "video") && strings.Contains(line, "codec") {
			hasVideo = true
			if strings.Contains(line, "h264") {
				videoCodec = "H.264"
			} else if strings.Contains(line, "mpeg2") {
				videoCodec = "MPEG-2"
			}
		}

		// Look for audio information
		if strings.Contains(line, "audio") && strings.Contains(line, "codec") {
			hasAudio = true
			if strings.Contains(line, "aac") {
				audioCodec = "AAC"
			} else if strings.Contains(line, "mp2") {
				audioCodec = "MP2"
			} else if strings.Contains(line, "ac3") || strings.Contains(line, "AC-3") {
				audioCodec = "AC-3"
			}
		}

		// Look for resolution
		re := regexp.MustCompile(`(\d+)x(\d+)`)
		if matches := re.FindStringSubmatch(line); len(matches) == 3 {
			width, _ = strconv.Atoi(matches[1])
			height, _ = strconv.Atoi(matches[2])
		}

		// Look for channels
		if strings.Contains(line, "channels") {
			re := regexp.MustCompile(`channels:\s*(\d+)`)
			if matches := re.FindStringSubmatch(line); len(matches) == 2 {
				channels, _ = strconv.Atoi(matches[1])
			}
		}
	}

	if hasVideo || hasAudio {
		service := models.ServiceInfo{
			ServiceID:   1,
			ServiceName: "Detected Service",
		}

		if hasVideo {
			service.VideoCodec = videoCodec
			service.VideoResolution = fmt.Sprintf("%dx%d", width, height)
			
			videoInfo := models.VideoInfo{
				Resolution: fmt.Sprintf("%dx%d", width, height),
				Codec:      videoCodec,
			}
			streamInfo.VideoInfo = append(streamInfo.VideoInfo, videoInfo)
		}

		if hasAudio {
			// Create multiple audio tracks for more realistic detection
			// Many broadcast streams have both 5.1 and stereo tracks when appropriate
			var audioTracks []models.AudioInfo
			
			// Determine what audio tracks to create based on detected codec
			if audioCodec == "AC-3" {
				// AC-3 streams often have both 5.1 and stereo tracks
				// Create 5.1 surround track (AUDIO1)
				audioTrack1 := models.AudioInfo{
					Channels: 6,
					Codec:    "AC-3",
				}
				audioTracks = append(audioTracks, audioTrack1)
				
				// Create stereo track (AUDIO2)
				audioTrack2 := models.AudioInfo{
					Channels: 2,
					Codec:    "AC-3",
				}
				audioTracks = append(audioTracks, audioTrack2)
				
				// Set legacy fields based on first track (5.1)
				service.AudioCodec = "AC-3"
				service.AudioChannels = 6
				
				// Set backward compatibility audio info to first track
				audioInfo := models.AudioInfo{
					Channels: 6,
					Codec:    "AC-3",
				}
				streamInfo.AudioInfo = append(streamInfo.AudioInfo, audioInfo)
			} else {
				// For other codecs (AAC, MP2), create based on detected channels
				audioTrack := models.AudioInfo{
					Channels: channels,
					Codec:    audioCodec,
				}
				audioTracks = append(audioTracks, audioTrack)
				
				// If we detected multi-channel audio, also add a stereo downmix
				if channels > 2 {
					stereoTrack := models.AudioInfo{
						Channels: 2,
						Codec:    audioCodec,
					}
					audioTracks = append(audioTracks, stereoTrack)
				}
				
				// Set legacy fields based on detected values
				service.AudioCodec = audioCodec
				service.AudioChannels = channels
				
				audioInfo := models.AudioInfo{
					Channels: channels,
					Codec:    audioCodec,
				}
				streamInfo.AudioInfo = append(streamInfo.AudioInfo, audioInfo)
			}
			
			// Set the audio tracks array in the service
			service.AudioTracks = audioTracks
		}

		streamInfo.Services = append(streamInfo.Services, service)
		streamInfo.TsSync = true
		logger.Log("GStreamer detection successful with %d audio tracks", len(service.AudioTracks))
	} else {
		streamInfo.Error = "No video or audio streams detected by GStreamer"
		logger.Error("GStreamer detection failed: no streams found")
	}

	return streamInfo, nil
}
