package detector

import (
	"bufio"
	"context"
	"fmt"
	"net"
	"os/exec"
	"regexp"
	"showfer-web/service/logger"
	"strings"
	"sync"
	"time"
)

// RtpSender represents information about an RTP sender
type RtpSender struct {
	IPAddress   string    `json:"ip_address"`
	LastSeen    time.Time `json:"last_seen"`
	PacketCount int64     `json:"packet_count"`
}

// RtpSenderDetector manages RTP sender detection for multicast groups
type RtpSenderDetector struct {
	mu              sync.RWMutex
	activeDetectors map[string]*multicastDetector
}

// multicastDetector handles detection for a specific multicast group using tcpdump
type multicastDetector struct {
	group           net.IP
	port            int
	senders         map[string]*RtpSender
	mu              sync.RWMutex
	ctx             context.Context
	cancel          context.CancelFunc
	stopped         bool
	networkInterface string
}

// Global detector instance
var globalDetector = &RtpSenderDetector{
	activeDetectors: make(map[string]*multicastDetector),
}

// GetRtpSenders returns the current list of RTP senders for a multicast address
func GetRtpSenders(multicastAddr string, port int) ([]*RtpSender, error) {
	return GetRtpSendersWithInterface(multicastAddr, port, "")
}

// GetRtpSendersWithInterface returns the current list of RTP senders for a multicast address using specified network interface
func GetRtpSendersWithInterface(multicastAddr string, port int, networkInterface string) ([]*RtpSender, error) {
	// Use default interface if none specified
	if networkInterface == "" {
		networkInterface = "eno2" // Default fallback
	}
	
	key := fmt.Sprintf("%s:%d:%s", multicastAddr, port, networkInterface)
	
	globalDetector.mu.RLock()
	detector, exists := globalDetector.activeDetectors[key]
	globalDetector.mu.RUnlock()
	
	if !exists {
		// Start detection for this multicast group
		err := StartRtpSenderDetectionWithInterface(multicastAddr, port, networkInterface)
		if err != nil {
			return nil, fmt.Errorf("failed to start RTP sender detection: %v", err)
		}
		
		// Wait a moment for detection to start
		time.Sleep(2 * time.Second)
		
		globalDetector.mu.RLock()
		detector, exists = globalDetector.activeDetectors[key]
		globalDetector.mu.RUnlock()
		
		if !exists {
			return []*RtpSender{}, nil
		}
	}
	
	detector.mu.RLock()
	defer detector.mu.RUnlock()
	
	var result []*RtpSender
	now := time.Now()
	
	// Filter out senders that haven't been seen in the last 10 seconds
	for _, sender := range detector.senders {
		if now.Sub(sender.LastSeen) < 10*time.Second {
			result = append(result, sender)
		}
	}
	
	return result, nil
}

// StartRtpSenderDetection starts detecting RTP senders for a multicast group using default interface
func StartRtpSenderDetection(multicastAddr string, port int) error {
	return StartRtpSenderDetectionWithInterface(multicastAddr, port, "")
}

// StartRtpSenderDetectionWithInterface starts detecting RTP senders for a multicast group using specified interface
func StartRtpSenderDetectionWithInterface(multicastAddr string, port int, networkInterface string) error {
	// Use default interface if none specified
	if networkInterface == "" {
		networkInterface = "eno2" // Default fallback
	}
	
	key := fmt.Sprintf("%s:%d:%s", multicastAddr, port, networkInterface)
	
	globalDetector.mu.Lock()
	defer globalDetector.mu.Unlock()
	
	// Check if already detecting for this group with this interface
	if _, exists := globalDetector.activeDetectors[key]; exists {
		return nil // Already detecting
	}
	
	group := net.ParseIP(multicastAddr)
	if group == nil {
		return fmt.Errorf("invalid multicast address: %s", multicastAddr)
	}
	
	if !group.IsMulticast() {
		return fmt.Errorf("address is not multicast: %s", multicastAddr)
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	detector := &multicastDetector{
		group:            group,
		port:             port,
		senders:          make(map[string]*RtpSender),
		ctx:              ctx,
		cancel:           cancel,
		networkInterface: networkInterface,
	}
	
	// Start the detection goroutine
	go detector.detect()
	
	globalDetector.activeDetectors[key] = detector
	logger.Log("Started RTP sender detection for %s:%d using tcpdump on interface %s", multicastAddr, port, networkInterface)
	
	return nil
}

// StopRtpSenderDetection stops detecting RTP senders for a multicast group on default interface
func StopRtpSenderDetection(multicastAddr string, port int) error {
	return StopRtpSenderDetectionWithInterface(multicastAddr, port, "")
}

// StopRtpSenderDetectionWithInterface stops detecting RTP senders for a multicast group on specified interface
func StopRtpSenderDetectionWithInterface(multicastAddr string, port int, networkInterface string) error {
	// Use default interface if none specified
	if networkInterface == "" {
		networkInterface = "eno2" // Default fallback
	}
	
	key := fmt.Sprintf("%s:%d:%s", multicastAddr, port, networkInterface)
	
	globalDetector.mu.Lock()
	defer globalDetector.mu.Unlock()
	
	detector, exists := globalDetector.activeDetectors[key]
	if !exists {
		return nil // Not detecting
	}
	
	detector.stop()
	delete(globalDetector.activeDetectors, key)
	logger.Log("Stopped RTP sender detection for %s:%d on interface %s", multicastAddr, port, networkInterface)
	
	return nil
}

// detect performs the actual RTP sender detection using tcpdump
func (d *multicastDetector) detect() {
	// Build tcpdump command
	// tcpdump -i eno2 -n udp port 1234 and dst host ************
	filter := fmt.Sprintf("udp port %d and dst host %s", d.port, d.group.String())
	
	logger.Log("Starting tcpdump capture for %s:%d with filter: %s on interface %s", d.group, d.port, filter, d.networkInterface)
	
	for {
		select {
		case <-d.ctx.Done():
			logger.Log("Stopping tcpdump capture for %s:%d on interface %s", d.group, d.port, d.networkInterface)
			return
		default:
			err := d.runTcpdump(filter)
			if err != nil {
				logger.Log("Tcpdump execution failed for %s:%d on interface %s - %v, retrying in 5 seconds", d.group, d.port, d.networkInterface, err)
				select {
				case <-d.ctx.Done():
					return
				case <-time.After(5 * time.Second):
					continue
				}
			}
		}
	}
}

// runTcpdump executes tcpdump and parses its output
func (d *multicastDetector) runTcpdump(filter string) error {
	// Create tcpdump command
	cmd := exec.CommandContext(d.ctx, "tcpdump", "-i", d.networkInterface, 
		"-n", "-l", filter)
	
	// Get stdout pipe
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %v", err)
	}
	
	// Get stderr pipe for error handling
	stderr, err := cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("failed to create stderr pipe: %v", err)
	}
	
	// Start the command
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start tcpdump: %v", err)
	}
	
	// Handle stderr in a separate goroutine
	go func() {
		scanner := bufio.NewScanner(stderr)
		for scanner.Scan() {
			line := scanner.Text()
			// Filter out common tcpdump info messages
			if !strings.Contains(line, "listening on") && 
			   !strings.Contains(line, "tcpdump:") &&
			   !strings.Contains(line, "packets captured") &&
			   !strings.Contains(line, "packets received") &&
			   !strings.Contains(line, "packets dropped") {
				logger.Log("Tcpdump stderr: %s", line)
			}
		}
	}()
	
	// Read and parse stdout
	scanner := bufio.NewScanner(stdout)
	// Regex to match tcpdump output: timestamp IP src.port > dst.port: protocol, ...
	// Example: 12:34:56.789012 IP *************.12345 > ************.1050: UDP, length 1316
	ipRegex := regexp.MustCompile(`IP\s+(\d+\.\d+\.\d+\.\d+)\.?\d*\s+>\s+` + regexp.QuoteMeta(d.group.String()))
	
	for scanner.Scan() {
		select {
		case <-d.ctx.Done():
			cmd.Process.Kill()
			return nil
		default:
		}
		
		line := scanner.Text()
		if line == "" {
			continue
		}
		
		// Parse tcpdump output to extract source IP
		matches := ipRegex.FindStringSubmatch(line)
		if len(matches) >= 2 {
			srcIP := matches[1]
			// Skip if it's the multicast address itself
			if srcIP != d.group.String() {
				d.updateSender(srcIP)
			}
		}
	}
	
	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading tcpdump output: %v", err)
	}
	
	// Wait for command to finish
	if err := cmd.Wait(); err != nil {
		// If context was cancelled, this is expected
		if d.ctx.Err() != nil {
			return nil
		}
		return fmt.Errorf("tcpdump command failed: %v", err)
	}
	
	return nil
}

// updateSender updates the sender information
func (d *multicastDetector) updateSender(senderAddr string) {
	d.mu.Lock()
	defer d.mu.Unlock()
	
	sender, exists := d.senders[senderAddr]
	if exists {
		sender.LastSeen = time.Now()
		sender.PacketCount++
	} else {
		d.senders[senderAddr] = &RtpSender{
			IPAddress:   senderAddr,
			LastSeen:    time.Now(),
			PacketCount: 1,
		}
		logger.Log("New RTP sender detected via tcpdump on interface %s: %s", d.networkInterface, senderAddr)
	}
}

// stop stops the detection for this multicast group
func (d *multicastDetector) stop() {
	d.mu.Lock()
	defer d.mu.Unlock()
	
	if d.stopped {
		return
	}
	
	d.stopped = true
	d.cancel()
}

// CleanupInactiveSenders removes senders that haven't been seen recently
func CleanupInactiveSenders() {
	globalDetector.mu.RLock()
	detectors := make([]*multicastDetector, 0, len(globalDetector.activeDetectors))
	for _, detector := range globalDetector.activeDetectors {
		detectors = append(detectors, detector)
	}
	globalDetector.mu.RUnlock()
	
	now := time.Now()
	for _, detector := range detectors {
		detector.mu.Lock()
		for ip, sender := range detector.senders {
			if now.Sub(sender.LastSeen) > 30*time.Second {
				delete(detector.senders, ip)
				logger.Log("Removed inactive RTP sender: %s", ip)
			}
		}
		detector.mu.Unlock()
	}
}

// StartCleanupTask starts a background task to cleanup inactive senders
func StartCleanupTask() {
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()
		
		for range ticker.C {
			CleanupInactiveSenders()
		}
	}()
} 