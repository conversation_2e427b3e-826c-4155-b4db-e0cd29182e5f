package database_sync

import (
	"fmt"
	"os"
	"os/exec"
	"showfer-web/config"
	"showfer-web/service/logger"
	"showfer-web/utils"
	"time"
)

// Global restore service instance
var restoreServiceInstance *RestoreService

// SetRestoreServiceInstance sets the global restore service instance
func SetRestoreServiceInstance(rs *RestoreService) {
	restoreServiceInstance = rs
}

// GetRestoreServiceInstance returns the global restore service instance
func GetRestoreServiceInstance() *RestoreService {
	return restoreServiceInstance
}

// RestoreService handles automatic database restoration for backup servers
type RestoreService struct {
	dumpPath      string
	checkInterval time.Duration
	lastModTime   time.Time
	stopChan      chan struct{}
}

// NewRestoreService creates a new database restore service
func NewRestoreService() *RestoreService {
	return &RestoreService{
		dumpPath:      "/tmp/showfer_db_dump.sql",
		checkInterval: 10 * time.Second, // Check every 10 seconds
		stopChan:      make(chan struct{}),
	}
}

// Start begins the periodic database restore monitoring process
func (rs *RestoreService) Start() {
	// Only start if this is a backup server
	if utils.GetServerType() != "backup" {
		logger.Log("Database restore service: Not a backup server, skipping restore monitoring")
		return
	}

	logger.Log("Database restore service: Starting automatic database restore monitoring")
	
	// Initialize last modification time if file exists
	// We subtract 1 second to ensure we catch any updates that might have happened during startup
	if info, err := os.Stat(rs.dumpPath); err == nil {
		rs.lastModTime = info.ModTime().Add(-1 * time.Second)
		logger.Log("Database restore service: Initial dump file found, last modified: %v (tracking from: %v)", info.ModTime(), rs.lastModTime)
	}
	
	ticker := time.NewTicker(rs.checkInterval)
	
	go func() {
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				rs.checkAndRestore()
			case <-rs.stopChan:
				logger.Log("Database restore service: Stopping")
				return
			}
		}
	}()
}

// Stop stops the restore service
func (rs *RestoreService) Stop() {
	close(rs.stopChan)
}

// checkAndRestore checks if the dump file has been updated and restores it if needed
func (rs *RestoreService) checkAndRestore() {
	// Only restore if this is a backup server
	if utils.GetServerType() != "backup" {
		return
	}

	// Check if dump file exists
	info, err := os.Stat(rs.dumpPath)
	if err != nil {
		// File doesn't exist, nothing to restore
		return
	}

	// Check if file has been modified since last check
	if !info.ModTime().After(rs.lastModTime) {
		// File hasn't been updated, nothing to do
		return
	}

	logger.Log("Database restore service: New dump file detected, last modified: %v", info.ModTime())
	
	// Update last modification time
	rs.lastModTime = info.ModTime()
	
	// Perform the database restoration
	rs.performRestore()
}

// performRestore restores the database from the dump file
func (rs *RestoreService) performRestore() {
	logger.Log("Database restore service: Starting database restoration from %s", rs.dumpPath)

	// Get database configuration
	dbConfig := config.GetDatabaseConfig()

	// First, drop all existing connections to the database
	rs.terminateConnections(dbConfig)

	// Drop and recreate the database
	if err := rs.recreateDatabase(dbConfig); err != nil {
		logger.Error("Database restore service: Failed to recreate database: %v", err)
		return
	}

	// Restore from dump file
	cmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", dbConfig.Port,
		"-U", dbConfig.User,
		"-d", dbConfig.DBName,
		"-f", rs.dumpPath,
		"--quiet",
	)

	// Set PGPASSWORD environment variable for authentication
	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))

	// Execute the restore command
	output, err := cmd.CombinedOutput()
	if err != nil {
		logger.Error("Database restore service: Failed to restore database: %v, output: %s", err, string(output))
		return
	}

	logger.Log("Database restore service: Successfully restored database from dump file")
}

// terminateConnections terminates all connections to the target database
func (rs *RestoreService) terminateConnections(dbConfig config.DatabaseConfig) {
	cmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", dbConfig.Port,
		"-U", dbConfig.User,
		"-d", "postgres", // Connect to postgres database to terminate connections
		"-c", fmt.Sprintf("SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%s' AND pid <> pg_backend_pid();", dbConfig.DBName),
		"--quiet",
	)

	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		logger.Warn("Database restore service: Failed to terminate connections: %v, output: %s", err, string(output))
	} else {
		logger.Log("Database restore service: Terminated existing database connections")
	}
}

// recreateDatabase drops and recreates the target database
func (rs *RestoreService) recreateDatabase(dbConfig config.DatabaseConfig) error {
	// Drop database
	dropCmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", dbConfig.Port,
		"-U", dbConfig.User,
		"-d", "postgres", // Connect to postgres database to drop the target
		"-c", fmt.Sprintf("DROP DATABASE IF EXISTS %s;", dbConfig.DBName),
		"--quiet",
	)

	dropCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))
	
	output, err := dropCmd.CombinedOutput()
	if err != nil {
		logger.Error("Database restore service: Failed to drop database: %v, output: %s", err, string(output))
		return err
	}

	logger.Log("Database restore service: Dropped existing database")

	// Create database
	createCmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", dbConfig.Port,
		"-U", dbConfig.User,
		"-d", "postgres", // Connect to postgres database to create the target
		"-c", fmt.Sprintf("CREATE DATABASE %s;", dbConfig.DBName),
		"--quiet",
	)

	createCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))
	
	output, err = createCmd.CombinedOutput()
	if err != nil {
		logger.Error("Database restore service: Failed to create database: %v, output: %s", err, string(output))
		return err
	}

	logger.Log("Database restore service: Created new database")
	return nil
}

// GetStatus returns the current restore service status
func (rs *RestoreService) GetStatus() map[string]interface{} {
	var dumpFileExists bool
	var dumpFileSize int64
	var dumpModTime time.Time

	if info, err := os.Stat(rs.dumpPath); err == nil {
		dumpFileExists = true
		dumpFileSize = info.Size()
		dumpModTime = info.ModTime()
	}

	return map[string]interface{}{
		"server_type":        utils.GetServerType(),
		"dump_file_path":     rs.dumpPath,
		"dump_file_exists":   dumpFileExists,
		"dump_file_size":     dumpFileSize,
		"dump_file_modified": dumpModTime,
		"last_checked":       rs.lastModTime,
		"check_interval":     rs.checkInterval.String(),
	}
} 