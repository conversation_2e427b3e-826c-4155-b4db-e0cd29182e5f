package storage

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"showfer-web/models"
	"showfer-web/service/logger"
	"strings"
)

// BackblazeService handles Backblaze B2 operations
type BackblazeService struct {
	settings models.CloudStorageSettings
	authToken string
	apiURL    string
	downloadURL string
}

// NewBackblazeService creates a new BackblazeService
func NewBackblazeService(settings models.CloudStorageSettings) *BackblazeService {
	return &BackblazeService{
		settings: settings,
	}
}

// AuthorizeAccount authorizes with Backblaze B2 API
func (bs *BackblazeService) AuthorizeAccount() error {
	url := "https://api.backblazeb2.com/b2api/v2/b2_authorize_account"
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create auth request: %v", err)
	}
	
	req.SetBasicAuth(bs.settings.BackblazeKeyID, bs.settings.BackblazeAppKey)
	
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to authorize account: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("authorization failed with status %d: %s", resp.StatusCode, string(body))
	}
	
	var authResp struct {
		AuthorizationToken string `json:"authorizationToken"`
		APIURL            string `json:"apiUrl"`
		DownloadURL       string `json:"downloadUrl"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
		return fmt.Errorf("failed to decode auth response: %v", err)
	}
	
	bs.authToken = authResp.AuthorizationToken
	bs.apiURL = authResp.APIURL
	bs.downloadURL = authResp.DownloadURL
	
	logger.Log("Successfully authorized with Backblaze B2")
	return nil
}

// ListBuckets retrieves list of buckets from Backblaze B2
func (bs *BackblazeService) ListBuckets() ([]models.BackblazeBucket, error) {
	if bs.authToken == "" {
		if err := bs.AuthorizeAccount(); err != nil {
			return nil, err
		}
	}
	
	url := bs.apiURL + "/b2api/v2/b2_list_buckets"
	
	payload := map[string]interface{}{
		"accountId": bs.settings.BackblazeKeyID,
	}
	
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create list buckets request: %v", err)
	}
	
	req.Header.Set("Authorization", bs.authToken)
	req.Header.Set("Content-Type", "application/json")
	
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to list buckets: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("list buckets failed with status %d: %s", resp.StatusCode, string(body))
	}
	
	var listResp struct {
		Buckets []struct {
			BucketID   string `json:"bucketId"`
			BucketName string `json:"bucketName"`
			BucketType string `json:"bucketType"`
		} `json:"buckets"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&listResp); err != nil {
		return nil, fmt.Errorf("failed to decode list buckets response: %v", err)
	}
	
	var buckets []models.BackblazeBucket
	for _, bucket := range listResp.Buckets {
		buckets = append(buckets, models.BackblazeBucket{
			BucketID:   bucket.BucketID,
			BucketName: bucket.BucketName,
			BucketType: bucket.BucketType,
		})
	}
	
	return buckets, nil
}

// UploadFile uploads a file to Backblaze B2
func (bs *BackblazeService) UploadFile(file multipart.File, filename, location string) error {
	if bs.authToken == "" {
		if err := bs.AuthorizeAccount(); err != nil {
			return err
		}
	}
	
	// Get upload URL for the bucket
	uploadURL, uploadToken, err := bs.getUploadURL()
	if err != nil {
		return fmt.Errorf("failed to get upload URL: %v", err)
	}
	
	// Prepare file path in bucket
	filePath := strings.TrimPrefix(location, "/") + filename
	if strings.HasPrefix(filePath, "/") {
		filePath = strings.TrimPrefix(filePath, "/")
	}
	
	// Read file content
	fileContent, err := io.ReadAll(file)
	if err != nil {
		return fmt.Errorf("failed to read file content: %v", err)
	}
	
	// Create upload request
	req, err := http.NewRequest("POST", uploadURL, bytes.NewReader(fileContent))
	if err != nil {
		return fmt.Errorf("failed to create upload request: %v", err)
	}
	
	req.Header.Set("Authorization", uploadToken)
	req.Header.Set("X-Bz-File-Name", filePath)
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("X-Bz-Content-Sha1", "unverified")
	
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to upload file: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(body))
	}
	
	logger.Log("Successfully uploaded file %s to Backblaze B2", filename)
	return nil
}

// getUploadURL gets an upload URL for the configured bucket
func (bs *BackblazeService) getUploadURL() (string, string, error) {
	url := bs.apiURL + "/b2api/v2/b2_get_upload_url"
	
	payload := map[string]interface{}{
		"bucketId": bs.settings.BackblazeBucket,
	}
	
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return "", "", fmt.Errorf("failed to marshal request: %v", err)
	}
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", "", fmt.Errorf("failed to create get upload URL request: %v", err)
	}
	
	req.Header.Set("Authorization", bs.authToken)
	req.Header.Set("Content-Type", "application/json")
	
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", "", fmt.Errorf("failed to get upload URL: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", "", fmt.Errorf("get upload URL failed with status %d: %s", resp.StatusCode, string(body))
	}
	
	var uploadResp struct {
		UploadURL   string `json:"uploadUrl"`
		AuthToken   string `json:"authorizationToken"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&uploadResp); err != nil {
		return "", "", fmt.Errorf("failed to decode upload URL response: %v", err)
	}
	
	return uploadResp.UploadURL, uploadResp.AuthToken, nil
}

// TestConnection tests the connection to Backblaze B2
func (bs *BackblazeService) TestConnection() error {
	if err := bs.AuthorizeAccount(); err != nil {
		return err
	}
	
	// Try to list buckets as a connection test
	_, err := bs.ListBuckets()
	return err
}
