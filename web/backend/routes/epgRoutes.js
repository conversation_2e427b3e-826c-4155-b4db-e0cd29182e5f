const express = require("express");
const fs = require("fs");
const path = require("path");
const { exec } = require("child_process");

const router = express.Router();
const EPG_DIR = path.join(__dirname, "../../data/epg");

// Get a list of all available EPG files
router.get("/", (req, res) => {
  try {
    if (!fs.existsSync(EPG_DIR)) {
      return res.status(404).json({ error: "EPG directory not found" });
    }

    const files = fs
      .readdirSync(EPG_DIR)
      .filter((file) => file.endsWith(".xml"))
      .map((file) => ({
        filename: file,
        path: `/data/epg/${file}`,
      }));

    res.json({ files });
  } catch (error) {
    console.error("Error listing EPG files:", error);
    res.status(500).json({ error: "Failed to list EPG files" });
  }
});

// Get a list of all schedule short_ids for EPG
router.get("/schedules", (req, res) => {
  try {
    // Execute SQL query to get schedule short_ids from the database
    const query = `
      SELECT id, name, short_id 
      FROM schedules 
      WHERE short_id IS NOT NULL AND short_id != ''
      ORDER BY id
    `;

    exec(
      `psql -U postgres -d traffiq -c "${query}" -t -A -F "," --no-align`,
      (error, stdout, stderr) => {
        if (error) {
          console.error("Error querying database:", error);
          return res.status(500).json({ error: "Failed to query schedules" });
        }

        if (stderr) {
          console.error("Database stderr:", stderr);
        }

        // Parse CSV output
        const schedules = stdout
          .trim()
          .split("\n")
          .filter((line) => line.trim() !== "")
          .map((line) => {
            const [id, name, shortId] = line.split(",");
            return {
              id: parseInt(id, 10),
              name,
              short_id: shortId,
            };
          });

        res.json({ schedules });
      }
    );
  } catch (error) {
    console.error("Error listing schedules:", error);
    res.status(500).json({ error: "Failed to list schedules" });
  }
});

// Get a specific EPG file by name
router.get("/:filename", (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(EPG_DIR, filename);

    // Check if the file exists
    if (!fs.existsSync(filePath)) {
      // If the specific file doesn't exist, try to find any XML file
      const files = fs
        .readdirSync(EPG_DIR)
        .filter((file) => file.endsWith(".xml"));
      if (files.length === 0) {
        return res.status(404).json({ error: "No EPG files found" });
      }

      // Return the first available XML file
      const fallbackPath = path.join(EPG_DIR, files[0]);
      res.sendFile(fallbackPath);
    } else {
      // Send the requested file
      res.sendFile(filePath);
    }
  } catch (error) {
    console.error(`Error serving EPG file ${req.params.filename}:`, error);
    res.status(500).json({ error: "Failed to serve EPG file" });
  }
});

module.exports = router;
