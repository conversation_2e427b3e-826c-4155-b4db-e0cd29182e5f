// FileStatusUpdate represents a status update for a file
export interface FileStatusUpdate {
  id: number;
  status: number;
}

// ConvertItem represents a file in the system
export interface ConvertItem {
  id: number;
  filename: string;
  location: string;
  duration: number;
  status: number;
  size: number;
  storage_type: number;
  created_at: string;
  updated_at: string;
  c_location: string;
  name: string;
  description: string;
  episode: string;
  width?: number;
  height?: number;
  fps?: number;
  video_codec?: string;
  audio_codec?: string;
  bitrate?: number;
  recorder_id?: number;
  codec_settings_version?: number;
}

// ConvertItemUpdateInput represents the input for updating a convert item
export interface ConvertItemUpdateInput {
  name: string;
  description: string;
  episode: string;
}

// ConvertItemListResult represents the result of listing convert items
export interface ConvertItemListResult {
  items: ConvertItem[];
  total_items: number;
  total_pages: number;
  page: number;
  limit: number;
}

// FileStatus represents the status of a file
export enum FileStatus {
  Success = 0,
  Queue = 1,
  Failed = 2,
  Transcoding = 3,
}

// StorageType represents the type of storage
export enum StorageType {
  Local = 0,
  S3 = 1,
}
